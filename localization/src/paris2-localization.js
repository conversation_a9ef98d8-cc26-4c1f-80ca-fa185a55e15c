import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";
import XHR from "i18next-xhr-backend";

import "./set-public-path";
import supportedLanguages from "./supportedLanguages";

let initialized = false;

const { NODE_ENV } = process.env;
console.log("NODE_ENV:", NODE_ENV);

const isDev = NODE_ENV === "development";

let currentLanguage = null;

const callbacks = [];

export async function getI18n() {
  if (initialized) {
    return i18n;
  }
  const result = await i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .use(XHR)
    // init i18next
    // for all options read: https://www.i18next.com/overview/configuration-options
    .init({
      // lng: 'en',
      fallbackLng: ["en"],
      debug: true,
      interpolation: {
        escapeValue: false // not needed for react as it escapes by default
      },
      backend: {
        loadPath: isDev
          ? "https://localhost:9010/locales/{{lng}}/{{ns}}.json"
          : "/static/locales/{{lng}}/{{ns}}.json",
        crossDomain: true
      }
    });
  console.log(result);
  initialized = true;
  return i18n;
}

export function getSupportedLanguages() {
  return supportedLanguages;
}

export function getLanguageByKey(languageKey) {
  const langPrefix = languageKey.split("-")[0];
  const lang = supportedLanguages.find(
    ({ key }) => languageKey === key || langPrefix === key
  );
  console.log("getLanguageByKey", langPrefix, languageKey, lang);
  return lang || supportedLanguages[0];
}

const onLanguageChanged = async language => {
  const newLanguage = getLanguageByKey(language);
  if (currentLanguage === null || newLanguage.key !== currentLanguage.key) {
    console.log("language changed:", newLanguage, currentLanguage);
    currentLanguage = newLanguage;

    callbacks.forEach(callback =>
      callback({
        i18n,
        languages: getSupportedLanguages(),
        currentLanguage
      })
    );
  }
};

export const initialize = async () => {
  if (!initialized) {
    await getI18n();
    i18n.on("languageChanged", onLanguageChanged);
    currentLanguage = i18n.language;
  }
  callbacks.forEach(callback =>
    callback({
      i18n,
      languages: getSupportedLanguages(),
      currentLanguage
    })
  );
};

export const addCallback = async callback => {
  if (!callbacks.includes(callback)) {
    callbacks.push(callback);
  }
};

export const removeCallback = async callback => {
  const index = callbacks.findIndex(callback);
  callbacks.splice(index, 1);
};
