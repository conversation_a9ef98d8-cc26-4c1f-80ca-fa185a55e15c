{"name": "paris2-localization", "repository": "*****************:fleetshipteam/paris2-web-base.git", "author": "", "license": "ISC", "version": "0.1.0", "description": "", "scripts": {"lint": "eslint src", "start": "webpack-dev-server --mode=development --server-type https", "test": "jest --passWithNoTests", "build": "webpack --mode=production", "deploy": "./deploy.sh", "analyze": "webpack --mode=production --env.analyze=true", "prettier": "prettier --write './**'", "watch-tests": "jest --watch", "coverage": "jest --coverage"}, "dependencies": {"i18next": "^23.8.3", "i18next-browser-languagedetector": "^7.0.2", "i18next-xhr-backend": "^3.2.2"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/preset-env": "^7.9.0", "@babel/preset-react": "^7.9.1", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "autoprefixer": "9.7.4", "babel-core": "6.26.3", "babel-eslint": "^11.0.0-beta.2", "babel-jest": "^25.1.0", "babel-loader": "^8.1.0", "babel-plugin-styled-components": "^1.10.7", "clean-webpack-plugin": "3.0.0", "concurrently": "^5.1.0", "css-loader": "^3.4.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-config-react-important-stuff": "^2.0.0", "eslint-plugin-prettier": "^3.1.2", "identity-obj-proxy": "^3.0.0", "jest": "^25.1.0", "jest-cli": "^25.1.0", "kremling-loader": "^1.0.2", "postcss-loader": "3.0.0", "prettier": "^2.0.1", "pretty-quick": "^2.0.1", "react-i18next": "^14.0.5", "single-spa-react": "^6.0.1", "style-loader": "^1.1.3", "styled-components": "^5.0.1", "systemjs-webpack-interop": "^2.0.0", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-config-single-spa-react": "4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}}