/* eslint-disable react/prop-types */
/* eslint-disable no-console */
import React from "react";
import { Faq } from "./components/Faq";
import * as localization from "@paris2/localization";
import "./scss/faq.scss";
import { BrowserRouter, Route, Switch } from "react-router-dom";

export default class Root extends React.Component {
  state = {
    hasError: false,
    i18n: null,
    languages: []
  };

  constructor(props) {
    super(props);
  }

  componentDidCatch(error, info) {
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, currentLanguage }) => {
    this.setState({ i18n, currentLanguage });
  };

  componentDidUnmount = async () => {
    localization.removeCallback(this.onLanguageChanged);
  };

  componentDidMount = async () => {
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
  };

  render() {
    const { i18n, currentLanguage } = this.state;
    if (!i18n || !currentLanguage) return null;
    return (
      <BrowserRouter>
        <Switch>
          <Route exact path="/faq">
            <Faq history={this.props.history} />
          </Route>
          <Route exact path="/terms-and-conditions">
            <Faq history={this.props.history} />
          </Route>
          <Route exact path="/registration-guide-manning-agents">
            <Faq history={this.props.history} />
          </Route>
          <Route path="*">
            <Faq history={this.props.history} />
          </Route>
        </Switch>
      </BrowserRouter>
    );
  }
}
