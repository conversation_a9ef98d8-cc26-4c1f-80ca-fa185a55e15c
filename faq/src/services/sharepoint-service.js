/* eslint-disable arrow-body-style */
/* eslint-disable @typescript-eslint/indent */
/* eslint-disable no-irregular-whitespace */
import axios from 'axios';

const PAGE_MAP = {
  faq: 7,
  'terms-and-conditions': 2,
  'registration-guide-manning-agents': 14,
};

const { TABLEAU_PROXY_HOST, SITE_NAME } = process.env;
/**
 * GET RSS feed for site https://www.fleetship.com
 */
export const getSharepointData = async (pagePath) => {
  const page = PAGE_MAP[pagePath];
  return axios.get(`${TABLEAU_PROXY_HOST}/get-sp-data/${SITE_NAME}/Site Pages/${page}`);
};
