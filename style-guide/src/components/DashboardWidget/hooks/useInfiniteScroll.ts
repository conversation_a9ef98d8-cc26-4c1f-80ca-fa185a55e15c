import { useRef, useCallback, useEffect } from "react";
import { UseInfiniteScrollProps } from "../types/types";

/**
 * This hook abstracts the entire logic for implementing an
 * infinite scroll container. It handles scroll event listeners and also fetches
 * more data if the initial content doesn't fill the screen.
 *
 * @returns A `ref` to be attached to the scrollable container element and an
 * `onScroll` handler function.
 */
export const useInfiniteScroll = ({
  fetchNextPage,
  isFetchingNextPage,
  hasNextPage,
  dataLength,
}: UseInfiniteScrollProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback(() => {
    const element = containerRef.current;
    if (!element || !hasNextPage || isFetchingNextPage) return;

    const { scrollTop, scrollHeight, clientHeight } = element;
    const threshold = 300; // How many pixels from the bottom to trigger a fetch

    if (scrollHeight - scrollTop - clientHeight < threshold) {
      fetchNextPage();
    }
  }, [fetchNextPage, isFetchingNextPage, hasNextPage]);

  // This effect checks if the initial data fills the viewport. If not, it fetches more.
  useEffect(() => {
    const checkAndFetch = () => {
      const element = containerRef.current;
      if (!element || !hasNextPage || isFetchingNextPage) return;

      const contentIsShort = element.scrollHeight <= element.clientHeight;
      if (contentIsShort) {
        fetchNextPage();
      }
    };

    // A small timeout allows the DOM to update after new data is rendered.
    const timer = setTimeout(checkAndFetch, 150);
    return () => clearTimeout(timer);
  }, [dataLength, hasNextPage, isFetchingNextPage, fetchNextPage]);

  return { containerRef, handleScroll };
};
