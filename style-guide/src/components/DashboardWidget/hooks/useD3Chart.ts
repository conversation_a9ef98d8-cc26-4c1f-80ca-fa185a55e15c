import { useMemo } from 'react';
import * as d3 from 'd3';

interface D3ChartConfig {
  vessels: any[];
  valueHeaders: string[];
  badgeColors: string[];
  valueDomain: [number, number];
  chartWidth: number;
  heightPerBar: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

export const useD3Chart = ({
  vessels,
  valueHeaders,
  badgeColors,
  valueDomain,
  chartWidth,
  heightPerBar,
  margin,
}: D3ChartConfig) => {
  const chartHeight = useMemo(() => vessels.length * heightPerBar, [vessels.length, heightPerBar]);
  const totalHeight = chartHeight + margin.top + margin.bottom;

  const xScale = useMemo(
    () =>
      d3
        .scaleLinear()
        .domain(valueDomain)
        .range([0, chartWidth - margin.left - margin.right])
        .clamp(true),
    [valueDomain, chartWidth, margin],
  );

  // Y Scale - Band scale for each unique vessel using its array index
  const yScale = useMemo(
    () =>
      d3
        .scaleBand()
        .domain(vessels.map((_, i) => i.toString())) // Use the unique index as the domain
        .range([margin.top, chartHeight + margin.top])
        .padding(0.4),
    [vessels, chartHeight, margin.top],
  );

  const barColorScale = useMemo(
    () => d3.scaleOrdinal<string, string>().domain(valueHeaders).range(badgeColors),
    [valueHeaders, badgeColors],
  );

  const textColorScale = useMemo(() => {
    const getTextColor = (bg: string) => (bg === '#fbc02d' ? 'black' : 'white');
    return d3
      .scaleOrdinal<string, string>()
      .domain(valueHeaders)
      .range(badgeColors.map(getTextColor));
  }, [valueHeaders, badgeColors]);

  const stackedBarData = useMemo(
    () =>
      vessels.map((vessel, i) => {
        let xOffset = 0;
        const segments = valueHeaders
          .map((key) => {
            const value = (vessel[key] as number) || 0;
            const segment = {
              key,
              value,
              x: xScale(xOffset),
              width: xScale(value),
              y: yScale(i.toString())!, // Use the unique index for y-positioning
              height: yScale.bandwidth(),
              vesselName: vessel.name,
              vesselIndex: i, // Add a unique index to the segment for later use
            };
            xOffset += value;
            return segment;
          });
        return { vesselName: vessel.name, segments, vesselIndex: i };
      }),
    [vessels, valueHeaders, xScale, yScale],
  );

  return {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  };
};