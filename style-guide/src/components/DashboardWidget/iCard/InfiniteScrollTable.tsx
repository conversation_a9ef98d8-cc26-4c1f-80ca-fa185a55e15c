import React, { useRef, useMemo, useEffect, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  ColumnDef,
  ExpandedState,
  getExpandedRowModel,
} from "@tanstack/react-table";
import {
  LiaSortSolid,
  LiaSortUpSolid,
  LiaSortDownSolid,
} from "react-icons/lia";
import classNames from "classnames";
import Spinner from "./Spinner";
import "./styles/CardTable.scss";

const renderSortIcon = (isSorted: false | string) => {
  if (isSorted === "asc") {
    return <LiaSortUpSolid />;
  }
  if (isSorted === "desc") {
    return <LiaSortDownSolid />;
  }
  return <LiaSortSolid />;
};

export interface InfiniteScrollTableProps<T extends object> {
  data: T[];
  columns: ColumnDef<T>[];
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  pagination: any;
  sorting: {
    sorting: Array<{ id: string; desc: boolean }>;
    onSortingChange: (params: Array<{ id: string; desc: boolean }>) => void;
  };
  tableContainerStyle?: React.CSSProperties;
  tableDataRowClassName?: string;
  rowSelection?: Record<string, boolean>;
  fetchNextPage: () => void;
  subRowKey?: any;
  onRowClick?: (rowData: T) => void;
}

function InfiniteScrollTable<T extends object>({
  data: initialData,
  columns,
  isLoading,
  isFetchingNextPage,
  pagination,
  sorting: sortingProps,
  tableContainerStyle,
  onRowClick,
  fetchNextPage,
}: Readonly<InfiniteScrollTableProps<T>>) {
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const data = useMemo(() => initialData, [initialData]);

  const handleSortingChange = React.useCallback(
    (updater: any) => {
      const newSorting =
        typeof updater === "function" ? updater(sortingProps.sorting) : updater;
      sortingProps.onSortingChange(newSorting);
    },
    [sortingProps.onSortingChange, sortingProps.sorting]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
      // Use the sorting state from props
      sorting: sortingProps.sorting,
    },
    manualSorting: true,
    enableExpanding: true,
    onExpandedChange: setExpanded,
    // Use the sorting change handler from props
    onSortingChange: handleSortingChange,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  const fetchMoreOnBottomReached = React.useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;

        if (scrollHeight > clientHeight) {
          const scrolledToBottom =
            scrollHeight - scrollTop - clientHeight < 300;

          if (
            scrolledToBottom &&
            !isFetchingNextPage &&
            pagination.page < pagination.totalPages
          ) {
            fetchNextPage();
          }
        }
      }
    },
    [fetchNextPage, isFetchingNextPage, pagination.page, pagination.totalPages]
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isFetchingNextPage) {
        fetchMoreOnBottomReached(tableContainerRef.current);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [fetchMoreOnBottomReached, initialData, isFetchingNextPage]);

  const tableContent = useMemo(() => {
    if (isLoading) {
      return (
        <div className="ra-loading-overlay">
          <Spinner />
        </div>
      );
    }

    if (table.getRowModel().rows.length === 0) {
      return (
        <div className="ra-loading-overlay">
          <p>No Results Found!</p>
        </div>
      );
    }

    return (
      <tbody>
        {table.getRowModel().rows.map((row) => (
          <tr
            key={row.id}
            className={classNames(
              "ra-tableRow",
              typeof onRowClick === "function" && "ra-clickable-row"
            )}
            onClick={() => onRowClick?.(row.original)}
          >
            {row.getVisibleCells().map((cell) => {
              const { isSticky, stickySide = "" } = (cell.column.columnDef
                .meta ?? {}) as Record<string, unknown>;
              const cellClassName = classNames({
                "ra-sticky-left": isSticky && stickySide === "left",
                "ra-sticky-right": isSticky && stickySide === "right",
                "ra-vesselNameCell": cell.column.id === "vessel",
              });

              return (
                <td key={cell.id} className={cellClassName}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              );
            })}
          </tr>
        ))}
        {isFetchingNextPage && (
          <tr>
            <td colSpan={columns.length} className="ra-spinnerContainer">
              <Spinner />
            </td>
          </tr>
        )}
      </tbody>
    );
  }, [
    isLoading,
    table.getRowModel().rows,
    onRowClick,
    isFetchingNextPage,
    columns.length,
  ]);

  const isTableEmpty = !isLoading && table.getRowModel().rows.length === 0;

  return (
    <div
      ref={tableContainerRef}
      onScroll={(e) => {
        fetchMoreOnBottomReached(e.currentTarget);
      }}
      className={classNames("ra-tableContainer", "table-responsive")}
      style={tableContainerStyle}
    >
      <table
        className={classNames("ra-table", {
          "ra-dashed-border": isTableEmpty,
        })}
      >
        {!isTableEmpty && (
          <thead className="ra-tableHeader">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const { isSticky, stickySide = "", headerAlign } = (header
                    .column.columnDef.meta ?? {}) as Record<string, unknown>;
                  const headerClassName = classNames("th", {
                    "ra-sticky-left": isSticky && stickySide === "left",
                    "ra-sticky-right": isSticky && stickySide === "right",
                    "text-center": headerAlign === "center",
                    "text-end": headerAlign === "right",
                  });
                  return (
                    <th
                      key={header.id}
                      onClick={header.column.getToggleSortingHandler()}
                      className={headerClassName}
                      style={{
                        minWidth: (header.column as any).columnDef.minSize,
                      }}
                    >
                      <div
                        className={classNames(
                          "ra-header-content",
                          `justify-content-${headerAlign || "start"}`
                        )}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {header.column.getCanSort() && ( // sorting icon is visible or not can be controled  dynamincally
                          <span className={"ra-sortIcon"}>
                            {renderSortIcon(
                              header.column.getIsSorted() as false | string
                            )}
                          </span>
                        )}
                      </div>
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
        )}
        {tableContent}
      </table>
    </div>
  );
}

export default InfiniteScrollTable;
