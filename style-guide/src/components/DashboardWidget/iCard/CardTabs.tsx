import React from "react";
import classNames from "classnames";
import "./styles/CardContainer.scss";

interface VesselTabsProps {
  tabs: string[];
  activeTab: string;
  IsAllTabVisible?: boolean;
  onTabChange: (tab: string) => void;
}

/**
 * A component focused only on rendering and managing tab navigation.
 */
export const CardTabs: React.FC<VesselTabsProps> = ({
  tabs,
  activeTab,
  IsAllTabVisible,
  onTabChange,
}) => {
  if (tabs.length === 0) {
    return null; // Don't render anything if there are no tabs
  }

  const displayTabs = IsAllTabVisible ? ["All", ...tabs] : tabs;

  return (
    <div className="ra-tabs-container">
      {displayTabs.map((tab) => (
        <button
          key={tab}
          onClick={() => onTabChange(tab)}
          className={classNames("ra-tab-button", { active: activeTab === tab })}
        >
          {tab}
          {activeTab === tab && (
            <span className="ra-active-tab-indicator"></span>
          )}
        </button>
      ))}
    </div>
  );
};
