import React, { useMemo, useState, useCallback } from "react";
import { RotateCw } from "lucide-react";
import classNames from "classnames";
import {
  Vessel,
  MultiSelectConfig,
  GridComponentType,
} from "../types/card-types";
import CardTable from "./CardTable";
import CardGrid from "./CardGrid";
import { CardModuleHeader } from "./CardModuleHeader";
import { CardDropdownSelectors } from "./CardDropdownSelectors";
import { ModuleModal } from "./ModuleModal";
import { CardTabs } from "./CardTabs";
import "./styles/CardContainer.scss";
import { ColumnDef } from "@tanstack/react-table";

export interface CardModuleProps {
  readonly title: string;
  readonly vessels: Vessel[];
  readonly multiVesselSelects: MultiSelectConfig[];

  readonly staticData: {
    readonly tabs: string[];
    readonly tableHeaders: string[];
    readonly badgeColors: string[];
  };

  // Callbacks
  readonly onRefresh: () => void;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly fetchNextPage: () => void;

  readonly sizeKey: "sm" | "md" | "lg";

  // State flags
  readonly visibleConfig: {
    readonly IsiconRenderVisible?: boolean;
    readonly IsenLargeIconVisible?: boolean;
    readonly IsVesselSelectVisible?: boolean;
    readonly IsAlltabsVisible?: boolean;
    readonly IsLastUpdatedVisible: boolean;
    readonly IsRefereshIconVisible: boolean;
    readonly vesselSelectPosition?: "before" | "after";
  };
  columns: ColumnDef<Vessel>[];
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;
  readonly pagination: any; // Using `any` for simplification as the exact type is complex

  readonly componentView: {
    readonly gridComponent?: GridComponentType;
    readonly defaultComponent?: "list" | "grid";
  };
}

export default function CardModule({
  title,
  vessels,
  staticData,
  visibleConfig,
  multiVesselSelects = [],
  componentView,
  sizeKey = "md",
  onRefresh,
  onSendEmail,
  onVesselClick,
  fetchNextPage,
  isFetchingNextPage,
  isLoading,
  pagination,
  columns: columnsProp,
}: Readonly<CardModuleProps>) {
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    componentView?.defaultComponent || "list"
  );
  const [activeTab, setActiveTab] = useState<string>(() =>
    visibleConfig?.IsAlltabsVisible ? "All" : staticData.tabs[0] || "All"
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => [])
  );

  const filteredVessels = useMemo(() => {
    if (!vessels) return [];

    let filteredData = [...vessels];

    // Filter by Active Tab
    if (activeTab !== "All") {
      filteredData = filteredData.filter((v: Vessel) => v.type === activeTab);
    }

    // Filter 1: Vessel Name (selectStates[0])
    const vesselNameSelections = selectStates[0];
    if (vesselNameSelections?.length > 0) {
      filteredData = filteredData.filter((vessel) =>
        vesselNameSelections.includes(vessel.name)
      );
    }

    // Filter 2: Level RA (selectStates[1])
    const levelRaSelections = selectStates[1];
    if (levelRaSelections?.length > 0) {
      filteredData = filteredData.filter((vessel: Vessel) =>
        levelRaSelections.includes(vessel.vesselData[1] as string)
      );
    }

    return filteredData;
  }, [vessels, activeTab, selectStates]);

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback(
    (index: number, newSelected: string[]) => {
      setSelectStates((prevStates) => {
        const newStates = [...prevStates];
        newStates[index] = newSelected;
        return newStates;
      });
    },
    []
  );

  const hasVesselsData = filteredVessels && filteredVessels.length > 0;
  const finalIsiconRenderVisible = hasVesselsData
    ? visibleConfig.IsiconRenderVisible
    : false;
  const finalIsenLargeIconVisible = hasVesselsData
    ? visibleConfig.IsenLargeIconVisible
    : false;

  const renderViewContent = () =>
    viewMode === "list" ? (
      <CardTable
        vessels={filteredVessels}
        columns={columnsProp} // Pass the columns array here
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage ?? false}
        isLoading={isLoading ?? false}
        fetchNextPage={fetchNextPage}
      />
    ) : (
      <CardGrid
        vessels={filteredVessels}
        tableHeaders={staticData.tableHeaders}
        badgeColors={staticData.badgeColors}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        gridComponent={componentView?.gridComponent}
      />
    );

  const renderModuleCore = () => (
    <>
      <CardModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModalOpen}
        IsiconRenderVisible={finalIsiconRenderVisible}
        IsenLargeIconVisible={finalIsenLargeIconVisible}
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      {visibleConfig.IsLastUpdatedVisible && (
        <div className="ra-last-updated-container">
          <p className="ra-last-updated-text">
            Last Updated on:{" "}
            {`${lastUpdated.toLocaleDateString(undefined, {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })} ${lastUpdated.toLocaleTimeString(undefined, {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            })}`}
            {visibleConfig.IsRefereshIconVisible && (
              <RotateCw onClick={handleRefresh} className="ra-refresh-icon" />
            )}
          </p>
        </div>
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "before" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {visibleConfig.IsAlltabsVisible && (
        <CardTabs
          tabs={staticData.tabs}
          activeTab={activeTab}
          IsAllTabVisible={visibleConfig.IsAlltabsVisible}
          onTabChange={setActiveTab}
        />
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "after" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {renderViewContent()}
    </>
  );

  return (
    <>
      <div
        className={classNames("ra-vessel-card-container", `size-${sizeKey}`)}
      >
        {renderModuleCore()}
      </div>

      {isModalOpen && (
        <ModuleModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          sizeKey={sizeKey}
        >
          {renderModuleCore()}
        </ModuleModal>
      )}
    </>
  );
}
