import React from "react";
import { CardDropdownSelectGroupProps } from "../types/card-types";
import { CardDropdown } from "./CardDropdown";
import "./styles/CardSelectGroup.scss";

/**
 * A simple wrapper component for the VesselDropdown.
 * Wrapped with React.memo because it's a pure component.
 * It will only re-render if its props change.
 */
export const CardDropdownSelectGroup: React.FC<CardDropdownSelectGroupProps> = React.memo(
  ({
    index,
    config,
    selectedItems,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible,
  }) => {
    // Move the hook to the top, before any conditionals
    const handleSelectionChange = React.useCallback(
      (newSelected: readonly string[]) => {
        onChange(index, newSelected as string[]);
      },
      [index, onChange]
    );

    if (!groups) {
      console.error("VesselSelectGroup: `groups` prop is missing.");
      return null;
    }

    return (
      <div className="raSelectWrapper">
        <CardDropdown
          groups={groups}
          selectedItems={selectedItems}
          onSelectionChange={handleSelectionChange}
          placeholder={config.placeholder}
          width={config.width ?? "200px"}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      </div>
    );
  }
);
