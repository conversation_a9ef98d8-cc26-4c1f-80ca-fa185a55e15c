import React from 'react';
import { useInfiniteScroll } from '../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import BarChart from '../common/BarChart';
import { DashboardGrid } from '../common/DashboardGrid';
import { CardGridProps } from '../types/card-types';
import './styles/CardGrid.scss';

export default function CardGrid({
  vessels,
  tableHeaders,
  badgeColors,
  chartData,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  gridComponent = 'bar',
  ...rest
}: Readonly<CardGridProps>) {
  // Use infinite scroll only for the 'list' or 'bar' view, not for 'pie'
  const isScrollable = gridComponent !== 'pie';

  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: true, // simplified, but the logic would be here
    dataLength: vessels?.length || 0,
  });
    console.log(vessels, 'vessels in cardgrid')

  const renderContent = () => {
   
    if (gridComponent === 'pie') {
      if (chartData) {
        return <DashboardGrid chartData={chartData} />;
      }
      return <div className="no-results-cell">Chart data not found.</div>;
    }

    // Default to Bar Chart
    const barChartData = vessels.map((vessel) => {
      const values: Record<string, number | string> = { name: vessel.name };
      tableHeaders
        .filter((h) => h !== 'Vessel' && h !== 'Action')
        .forEach((header, idx) => {
          values[header] = vessel.vesselData[idx];
        });
      return values;
    });

    if (vessels.length === 0) {
      return <div className="no-results-cell">No results found</div>;
    }

    return (
      <div className="ra-vessel-bar-chart-wrapper">
        <BarChart
          vessels={barChartData}
          valueHeaders={tableHeaders.filter((h) => h !== 'Vessel' && h !== 'Action')}
          badgeColors={badgeColors}
          valueDomain={[0, 250]}
          isModal={true}
          {...rest}
        />
      </div>
    );
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className="vessel-grid-root">
      {renderContent()}
      {/* {isFetchingNextPage && (
        <div className="loading-indicator">
          <Spinner />
        </div>
      )} */}
    </div>
  );
}
