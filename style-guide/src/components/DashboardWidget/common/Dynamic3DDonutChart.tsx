import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-3d";
import { useHighchartsDonut } from "../hooks/useHighchartsDonut";
import './styles/DonutChartCard.scss';

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface DonutChartProps {
  data: ChartDataItem[];
}

const Dynamic3DDonutChart: React.FC<DonutChartProps> = ({ data }) => {
  const { options, tooltipStyle } = useHighchartsDonut({ data });

  return (
    <div className="donut-chart-container">
      <style>{tooltipStyle}</style>
      <HighchartsReact 
        highcharts={Highcharts} 
        options={options} 
        containerProps={{ className: "donut-chart-highchart" }}
      />
    </div>
  );
};

export default Dynamic3DDonutChart;