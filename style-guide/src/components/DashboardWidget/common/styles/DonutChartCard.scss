.deficiency-count-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: 80px;
}

.deficiency-count-title {
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
}

.deficiency-total-count {
  font-size: 1.2rem;
  font-weight: bold;
  color: #1f4a70;
}

.vessel-grid-root {
  height: 300px;
  overflow-y: auto;
  position: relative;
}

.spinner-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.no-results-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1rem;
  color: #6b7280;
}

.donut-chart-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: 252px; // Adjusted height
}

.donut-chart-title {
  font-size: 0.8rem; // Adjusted font size
  font-weight: 500;
  color: #333;
}

.donut-chart-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  height: 200px;
}

.donut-chart-visual {
  height: 250px;
  width: 325px;
  justify-content: center;
}

.donut-chart-legend-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
}
.donut-chart-highchart{
    height: 200px;
    margin-top: -10px;
}

.donut-chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
}

.donut-chart-legend-item {
  display: flex;
  align-items: center;
}

.donut-chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  &.color-red {
    background-color: red;
  }
  &.color-yellow {
    background-color: yellow;
  }
  &.color-green {
    background-color: green;
  }
}

.donut-chart-legend-label {
  font-weight: 500;
  color: #555;
  margin-right: 0.25rem;
  font-size: 0.8rem;
}

.donut-chart-legend-value {
  font-weight: bold;
  color: #333;
  font-size: 0.8rem;
}

.dashboard-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}