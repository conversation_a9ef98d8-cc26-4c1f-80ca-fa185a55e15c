{"name": "paris2-landing-page", "version": "1.0.0", "private": true, "dependencies": {"@react-google-maps/api": "^2.19.2", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/google.maps": "^3.54.3", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.173", "@types/node": "^18.19.17", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "axios": "^0.21.4", "classnames": "2.5.1", "d3": "7.9.0", "downloadjs": "^1.4.7", "ga-4-react": "^0.1.281", "history": "^4.9.0", "html-react-parser": "^1.2.7", "lodash": "^4.17.21", "lucide-react": "0.525.0", "moment": "^2.29.4", "moment-timezone": "^0.5.37", "react": "^18.2.0", "react-bootstrap": "^1.6.0", "react-bootstrap-icons": "^1.5.0", "react-bootstrap-typeahead": "^6.3.2", "react-clock": "^3.0.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-i18next": "^11.8.15", "react-icons": "^4.2.0", "react-joyride": "^2.3.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-table": "^7.7.0", "react-twitter-embed": "^4.0.4", "react-youtube": "^7.13.1", "single-spa-react": "^6.0.1", "tableau-react": "^2.2.0", "typescript": "^4.1.2", "use-debounce": "^6.0.1", "web-vitals": "^1.0.1", "xml-js": "^1.6.11"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-syntax-jsx": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.7", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.12.7", "@babel/preset-react": "^7.12.7", "@babel/preset-typescript": "^7.12.7", "@babel/runtime": "^7.9.2", "@types/downloadjs": "^1.4.2", "@types/history": "^4.7.8", "@types/react": "^18.2.56", "@types/react-bootstrap-typeahead": "^5.1.6", "@types/react-clock": "^3.0.0", "@types/react-helmet": "^6.1.2", "@types/react-html-parser": "^2.0.1", "@types/react-router": "^5.1.12", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^4.24.0", "@typescript-eslint/parser": "^4.24.0", "babel-loader": "^8.2.2", "babel-plugin-styled-components": "^1.10.7", "clean-webpack-plugin": "^3.0.0", "css-loader": "^5.0.1", "dotenv": "^8.2.0", "eslint": "^7.27.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.3", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^5.1.3", "jest-transform-stub": "^2.0.0", "prettier": "2.3.0", "react-html-parser": "^2.0.2", "react-test-renderer": "^18.2.0", "sass": "^1.26.3", "style-loader": "^2.0.0", "svg-url-loader": "^7.1.1", "systemjs-webpack-interop": "^2.0.0", "tsconfig-paths-webpack-plugin": "^3.3.0", "unused-files-webpack-plugin": "^3.4.0", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-config-single-spa-react": "4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}, "scripts": {"start": "webpack-dev-server --mode=development --port 9070 --server-type https", "build": "webpack --mode=production", "test": "jest --runInBand --verbose", "test:watch": "jest --watch --verbose", "test:coverage": "jest --coverage", "lint": "tsc --noEmit && eslint src/**/*.ts{,x}", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}