import React, { useMemo, useEffect, useState } from 'react';
import { useInfiniteQuery } from '../../../hooks/useInfiniteQuery';
import { Vessel, MultiSelectConfig } from '../../../types/card-types';
import { cardModuleConfigs } from './CardModuleConfig';
import { CardModule } from '@paris2/styleguide';
export interface CardModuleContainerProps {
  // We're replacing the numerous props with a single configKey
  readonly configKey: string;
}

export default function CardModuleContainer({
  configKey,
}: Readonly<CardModuleContainerProps>): JSX.Element {
  // Use the configKey to get the configuration from the centralized object
  const config = cardModuleConfigs[configKey];
  const [processedMultiVesselSelects, setProcessedMultiVesselSelects] = useState<MultiSelectConfig[] | undefined>(undefined);

  if (!config) {
    console.error(`No configuration found for key: ${configKey}`);
    return <div>Error: Invalid configuration key provided.</div>;
  }

  const {
    title,
    fetchFn1,
    multiVesselSelects,
    staticData,
    visibleConfig,
    componentView,
    sizeKey,
    ...rest
  } = config;

  useEffect(() => {
    async function fetchGroups() {
      if (multiVesselSelects) {
        const processedSelects = await Promise.all(
          multiVesselSelects.map(async (selectConfig: any) => {
            if (typeof selectConfig.groups === 'function') {
              const groups = await selectConfig.groups();
              return { ...selectConfig, groups };
            }
            return selectConfig;
          })
        );
        setProcessedMultiVesselSelects(processedSelects);
      }
    }
    fetchGroups();
  }, [multiVesselSelects]);

  const { data, isLoading, isFetchingNextPage, fetchNextPage, refetch } = useInfiniteQuery(
    fetchFn1,
    { limit: 1000 },
  );

  const handleSendEmail = (vessel: Vessel) => {
    if (!vessel.risk_id) return;
    const url = `https://paris2-dev2.fleetship.com/risk-assessment/approval/${vessel.risk_id}`;
    window.open(url, '_blank');
  };

  const handleVesselClick = (vessel: Vessel) => {
    if (!vessel.vessel_ownership_id) return;
    const url = `https://paris2-dev2.fleetship.com/vessel/ownership/details/${vessel.vessel_ownership_id}`;
    window.open(url, '_blank');
  };

  const vessels = useMemo(() => data?.data ?? [], [data]);
  const pagination = data?.pagination ?? {
    totalItems: 0,
    totalPages: 0,
    page: 0,
    pageSize: 0,
  };

  // Memoize columns to prevent infinite re-renders - this ensures stable reference
  const stableColumns = useMemo(() => {
    // If columns are passed in config, use them; otherwise use empty array
    return config.columns || [];
  }, [config.columns]);

  return (
    <CardModule
      title={title}
      vessels={vessels}
      multiVesselSelects={processedMultiVesselSelects}
      pagination={pagination}
      isLoading={isLoading}
      isFetchingNextPage={isFetchingNextPage}
      fetchNextPage={fetchNextPage}
      onRefresh={refetch}
      onSendEmail={handleSendEmail}
      onVesselClick={handleVesselClick}
      staticData={staticData}
      visibleConfig={visibleConfig}
      componentView={componentView}
      sizeKey={sizeKey}
      columns={stableColumns}
      {...rest}
    />
  );
}
