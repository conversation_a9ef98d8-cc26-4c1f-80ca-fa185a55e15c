import { Vessel, IProjectListResponse } from '../../../types/card-types';
import { WidgetConstant } from './widget.constant';
import { fetchVesselOwnerships, getItineraries } from '../../../services/vm-widget-service';
import { getItineraryColumns } from './ColumnConfig';

// Mock fetch function for demonstration
const mockFetchFn = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<IProjectListResponse<Vessel>> => {
  console.log('Fetching with params:', params);
  await new Promise((resolve) => setTimeout(resolve, 1000));
  const mockData = Array.from({ length: params.limit }, (_, i) => ({
    name: `Vessel ${i + (params.page - 1) * params.limit}`,
    type: 'Statutory',
    vesselData: [`1`, `122`, `21`],
    vessel_ownership_id: i,
    risk_id: i,
  }));
  return {
    data: mockData,
    pagination: {
      totalItems: 100,
      totalPages: 5,
      page: params.page,
      pageSize: params.limit,
    },
  };
};

export const cardModuleConfigs: { [key: string]: any } = {
  [WidgetConstant.OWNER_FINANCIAL_REPORTING]: {
    title: 'Owner Financial Reporting',
    fetchFn1: mockFetchFn,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Level of R.A.',
        width: '300px',
        groups: fetchVesselOwnerships,
        isSearchBoxVisible: false,
        isSelectAllVisible: false,
      },
    ],
    staticData: {
      tabs: ['Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: true,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.ITINERARY_ETA]: {
    title: 'Itinerary (ETA)',
    fetchFn1: getItineraries,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    getItineraryColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: true,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },
};
