import React from 'react';
import { MailIcon } from "lucide-react";

export type <PERSON><PERSON>enderer<T> = (props: { row: { original: T } }) => React.ReactNode;

export type ColumnDef<T> = {
  id?: string;
  accessorKey?: string;
  header?: string | React.ReactNode;
  cell?: CellRenderer<T>;
  meta?: Record<string, any>;
  minSize?: number;
  enableSorting?: boolean;
};

 export const getItineraryColumns: ColumnDef<any>[] = [
    {
      accessorKey: "vessel_name",
      id: "vessel",
      header: "Vessel",
      cell: ({ row }) => (
        <button
        //   onClick={() => onVesselClick(row.original)}
          className="ra-cardNameButton"
        >
          {row.original.vessel_name}
        </button>
      ),
      meta: { isSticky: true, stickySide: "left" },
      minSize: 200,
    },
    // Add other columns here based on your example...
    // For the 'Action' column
    // {
    //   id: "action",
    //   header: "Action",
    //   meta: { isSticky: true, stickySide: "right" },
    //   cell: ({ row }) => (
    //     <div className="ra-emailButtonWrapper">
    //       <button
    //         // onClick={() => onSendEmail(row.original)}
    //         className="ra-emailButton"
    //       >
    //         <MailIcon size={20} />
    //       </button>
    //     </div>
    //   ),
    //   enableSorting: false,
    //   minSize: 90,
    // },
  ];