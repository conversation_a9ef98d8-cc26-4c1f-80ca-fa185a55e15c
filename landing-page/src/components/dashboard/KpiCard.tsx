import React, { useState, useEffect } from 'react';
import { StatCard } from '@paris2/styleguide';
import { getOwnerships } from '../../services/vessel-service';
import { getPrecomputedCompliances, VesselCompliance } from '../../services/seafarer-service';
import { VesselOwned } from '../../types/vessel';
import { VESSEL_TYPES } from '../../constants/vessel';
import { KeycloakProps } from '../../types/keycloak';

interface KpiCardProps {
  keycloak: KeycloakProps['keycloak'];
  onStatCardDataUpdate?: (cardType: string, data: { count: number; isLoading: boolean }) => void;
  children?: React.ReactNode;
}

export interface KpiCardRef {
  handleStatCardDataUpdate: (cardType: string, data: { count: number; isLoading: boolean }) => void;
}

interface StatCardData {
  surveys: { count: number; isLoading: boolean };
  deficiencies: { count: number; isLoading: boolean };
  riskAssessments: { count: number; isLoading: boolean };
  nonCompliantOcimfTankers: { count: number; isLoading: boolean };
}

const KpiCard = React.forwardRef<KpiCardRef, KpiCardProps>(({ keycloak, onStatCardDataUpdate, children }, ref) => {
  // State to hold StatCard data
  const [statCardData, setStatCardData] = useState<StatCardData>({
    surveys: { count: 0, isLoading: true },
    deficiencies: { count: 0, isLoading: true },
    riskAssessments: { count: 0, isLoading: true },
    nonCompliantOcimfTankers: { count: 0, isLoading: true },
  });

  // State for OCIMF compliance logic (for VM users)
  const [vesselList, setVesselList] = useState<(VesselOwned & { vessel_type?: string })[]>([]);
  const [isVesselLoading, setIsVesselLoading] = useState(false);
  const [ocimfComplianceData, setOcimfComplianceData] = useState<VesselCompliance[]>([]);
  const [isPreComputeApiCalled, setIsPreComputeApiCalled] = useState<boolean>(false);

  const isCurrentUserVm = keycloak?.tokenParsed?.email === '<EMAIL>';

  // Handler to update StatCard data from CardModuleContainer
  const handleStatCardDataUpdate = (
    cardType: string,
    data: { count: number; isLoading: boolean },
  ) => {
    setStatCardData((prev) => ({
      ...prev,
      [cardType]: data,
    }));
    
    // Also call the parent callback if provided
    if (onStatCardDataUpdate) {
      onStatCardDataUpdate(cardType, data);
    }
  };

  // Functions for OCIMF compliance logic (for VM users)
  const getMinimalVesselList = async () => {
    try {
      setIsVesselLoading(true);
      const minimalVesselList = await getOwnerships('f=name&f=id&f=vessel.id&f=vessel_type.type');
      const preparedVesselList = minimalVesselList?.data?.results?.map(vessel => ({
        id: vessel.id,
        name: vessel?.name,
        vessel_id: vessel?.vessel?.id,
        vessel_type: vessel?.vessel_type?.type,
      }));
      setVesselList(preparedVesselList || []);
      setIsVesselLoading(false);
    } catch (e) {
      console.log(e);
      setIsVesselLoading(false);
    }
  };

  const prepareAndSetComplianceData = (complianceData: VesselCompliance[]) => {
    const nonCompliantVesselIds = complianceData?.map(vessel => vessel.vessel_id);
    const preparedNonCompliantVessels = vesselList.filter(vessel =>
      nonCompliantVesselIds.includes(vessel.vessel_id)
    );

    // Update the stat card with the count of non-compliant vessels
    const newData = {
      count: preparedNonCompliantVessels.length,
      isLoading: false,
    };
    
    setStatCardData((prev) => ({
      ...prev,
      nonCompliantOcimfTankers: newData,
    }));

    // Also call the parent callback if provided
    if (onStatCardDataUpdate) {
      onStatCardDataUpdate('nonCompliantOcimfTankers', newData);
    }
  };

  const getOcimfComplianceList = async () => {
    const payload = {
      vessel_ids: vesselList
        ?.filter(vessel => vessel?.vessel_type === VESSEL_TYPES.TANKER)
        .map(vessel => vessel.vessel_id),
    };

    try {
      const ocimfComplianceResponse = await getPrecomputedCompliances(payload);
      setOcimfComplianceData(ocimfComplianceResponse.data);
      prepareAndSetComplianceData(ocimfComplianceResponse.data);
      setIsPreComputeApiCalled(true);
    } catch (error) {
      console.error('Error fetching OCIMF compliance data:', error);
      const errorData = {
        count: 0,
        isLoading: false,
      };
      
      setStatCardData((prev) => ({
        ...prev,
        nonCompliantOcimfTankers: errorData,
      }));

      // Also call the parent callback if provided
      if (onStatCardDataUpdate) {
        onStatCardDataUpdate('nonCompliantOcimfTankers', errorData);
      }
    }
  };

  // Effects for OCIMF compliance logic (only for VM users)
  useEffect(() => {
    if (isCurrentUserVm) {
      getMinimalVesselList();
    }
  }, [isCurrentUserVm]);

  useEffect(() => {
    if (isCurrentUserVm && vesselList?.length) {
      getOcimfComplianceList();
    }
  }, [vesselList, isCurrentUserVm]);

  // Expose the handleStatCardDataUpdate function via ref
  React.useImperativeHandle(ref, () => ({
    handleStatCardDataUpdate
  }));

  return (
    <>
      <div className="stat-card-wrapper">
        <StatCard
          title="Surveys and Certificate"
          count={statCardData.surveys.count}
          subTitle="Expiring within 30 days"
          isLoading={statCardData.surveys.isLoading}
        />
        <StatCard
          title="Deficiencies"
          count={statCardData.deficiencies.count}
          subTitle="Overdue"
          isLoading={statCardData.deficiencies.isLoading}
          onClick={() => {}}
        />
        <StatCard
          title="Unassigned Risk Assessment"
          count={statCardData.riskAssessments.count}
          isLoading={statCardData.riskAssessments.isLoading}
          onClick={() => {}}
        />
        <StatCard
          title="Non-Compliant OCIMF Tankers"
          count={statCardData.nonCompliantOcimfTankers.count}
          isLoading={statCardData.nonCompliantOcimfTankers.isLoading}
        />
      </div>
      {children}
    </>
  );
});

export default KpiCard;
