#!/bin/bash

declare -a files=(
  # import map
  "styled-components@5.2.1/dist/styled-components.cjs.min.js"
  "react@18.2/umd/react.production.min.js"
  "react-dom@18.2/umd/react-dom.production.min.js"
  "single-spa@6.0.0/lib/es2015/system/single-spa.min.js"
  "react-bootstrap@1.4.0/cjs/index.min.js"
  "react-i18next@11.3.4/dist/umd/react-i18next.js"
  "axios/dist/axios.min.js"

  # dev libraries
  "react@18.2/umd/react.development.js"
  "react-dom@18.2/umd/react-dom.development.js"
  "single-spa@6.0.0/lib/es2015/system/single-spa.dev.js"

  # loaded in index.html
  "import-map-overrides/dist/import-map-overrides.js"
  "systemjs/dist/system.min.js"
  "systemjs/dist/extras/amd.min.js"
  "systemjs/dist/extras/named-exports.min.js"
)

CDN_HOST='https://cdn.jsdelivr.net/npm'

for url in "${files[@]}"
do
   mkdir -p "./dist/$(dirname $url)"
   curl "$CDN_HOST/$url" -o "./dist/$url"
done