{"name": "paris2-external-config", "scripts": {"start": "webpack-dev-server --mode=development --port 9123", "lint": "eslint src", "test": "jest", "prettier": "prettier --write './**'", "build": "webpack --mode=production"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged && eslint src"}}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.7.4", "@babel/runtime": "^7.8.7", "@types/systemjs": "^6.1.0", "babel-eslint": "^11.0.0-beta.2", "babel-loader": "^8.0.6", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.1.1", "eslint": "^6.7.2", "eslint-config-important-stuff": "^1.1.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-prettier": "^3.1.1", "html-webpack-plugin": "^3.2.0", "husky": "^3.1.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "serve": "^11.2.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.9.0"}, "dependencies": {"history": "^4.10.1", "keycloak-js": "^19.0.3", "single-spa": "^4.4.2"}}