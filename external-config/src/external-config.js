import { registerApplication, start } from 'single-spa';
import Keycloak from 'keycloak-js';

import * as isActive from './activity-functions';

const { AUTH_SERVER_URL } = process.env;

// TODO: use paris2-auth module instead of directly access Keycloak
// TODO: use correct REALM and AUTH_CLIENT_ID

const REALM = 'keycloak-poc';
const AUTH_CLIENT_ID = 'poc-frontend';

const keycloakConfig = {
    url: AUTH_SERVER_URL,
    realm: REALM,
    clientId: AUTH_CLIENT_ID,
};

if (keycloakConfig.url.startsWith(':')) {
    keycloakConfig.url = `${window.location.protocol}//${window.location.hostname}${AUTH_SERVER_URL}`;
}

const kc = new Keycloak(keycloakConfig);
/**
 * Solution of P2-13091:
 * The fix is from https://github.com/keycloak/keycloak/issues/14742
 * the problem is that react-dom behavior is having conflicts with the iframe
 * which checking the login in older version of chrome
 * Therefore the fix is using query string instead of fragments to reduce page render
 * and remove check-sso to remove iframe to check the page constantly
 **/
kc.init({
    // onLoad: 'check-sso',
    promiseType: 'native',
    // silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
    responseMode: 'query',
}).then(authenticated => {
    if (authenticated) {
        registerApplication(
            '@paris2/navbar',
            () => System.import('@paris2/navbar'),
            isActive.navbar,
            { kc },
        );
        start();
    }
});
