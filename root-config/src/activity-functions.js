export function prefix(location, ...prefixes) {
    return prefixes.some(prefix => location.href.indexOf(`${location.origin}/${prefix}`) !== -1);
}

export function navbar(location) {
    // The navbar is always active
    return true;
}

export function notifications(location) {
    return prefix(location, 'notifications');
}

export function vessel(location) {
    return prefix(location, 'vessel');
}

export function seafarer(location) {
    return prefix(location, 'seafarer');
}

export function itemMaster(location) {
    return prefix(location, 'item-master');
}

export function vesselInspectionReport(location) {
    return prefix(location, 'inspections-audits') || prefix(location, 'form-b');
}

export function deficiency(location) {
    return prefix(location, 'deficiency');
}

export function qualityManagementSystem(location) {
    return prefix(location, 'quality-management-system');
}

export function shipParty(location) {
    return prefix(location, 'ship-party') || prefix(location, 'file-transfer');
}

export function tableau(location) {
    return prefix(location, 'tableau');
}

export function nova(location) {
    return prefix(location, 'nova') || prefix(location, 'dashboard');
}

export function landingPage(location) {
    return prefix(location, 'home') || prefix(location, 'worldmap');
}

export function faq(location) {
    return (
        prefix(location, 'faq') ||
        prefix(location, 'terms-and-conditions') ||
        prefix(location, 'registration-guide-manning-agents')
    );
}

export function importsheet(location) {
    return prefix(location, 'portage-bill');
}
export function externalReporting(location) {
    return prefix(location, 'external-reporting');
}

export function seafarerSurvey(location) {
    return prefix(location, 'survey');
}
export function qhse(location) {
    return prefix(location, 'qhse');
}

export function ownerReporting(location) {
    return prefix(location, 'owner-reporting');
}
export function reference(location) {
    return prefix(location, 'reference');
}
export function pms(location) {
    return prefix(location, 'pms');
}
export function riskAssessment(location) {
    return prefix(location, 'risk-assessment');
}