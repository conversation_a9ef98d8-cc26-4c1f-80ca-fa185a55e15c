import { registerApplication, start } from 'single-spa';
import { createBrowserHistory } from 'history';
import GA4React from 'ga-4-react';
import pathToRegexp from 'path-to-regexp';
import { toString, includes } from 'lodash';
import * as isActive from './activity-functions';
import notificationEvents from '@paris2/notification-events';
import auth from '@paris2/auth';

const publicRouters = [
    {
        path: '/faq',
        module: '@paris2/faq',
        activityFn: isActive.faq,
    },
    {
        path: '/terms-and-conditions',
        module: '@paris2/faq',
        activityFn: isActive.faq,
    },
    {
        path: '/registration-guide-manning-agents',
        module: '@paris2/faq',
        activityFn: isActive.faq,
    },
    {
        path: '/file-transfer/upload-external',
        module: '@paris2/ship-party',
        activityFn: isActive.shipParty,
    },
    {
        path: '/file-transfer/download-external',
        module: '@paris2/ship-party',
        activityFn: isActive.shipParty,
    },
    {
        path: '/form-b/:id',
        module: '@paris2/inspections-audits',
        activityFn: isActive.vesselInspectionReport,
    },
];

const homeUrls = [
    {
        href: '/home',
        role: 'nova|view',
    },
    {
        href: '/vessel',
        role: 'vessel|view',
    },
    {
        href: '/seafarer',
        role: 'seafarer|view',
    },
    {
        href: '/item-master',
        role: 'item-master|view',
    },
    {
        href: '/ship-party',
        role: 'ship-party|view',
    },
    {
        href: '/tableau',
        role: 'tableau|view',
    },
    {
        href: '/nova',
        role: 'nova|view',
    },
    {
        href: '/inspections',
        role: 'vessel|view',
    },
    {
        href: '/templates',
        role: 'vessel|view',
    },
    {
        href: '/reference',
        role: 'vessel|admin',
    },
];

const history = createBrowserHistory();
const path = (/#!(\/.*)$/.exec(location.hash) || [])[1];

/**
 * fix the redirect path
 * @param {*} path path to redirect to
 */
const fixRedirectPath = path => {
    let newPath = path;
    if (path.includes('&') && !path.includes('?')) {
        newPath = path.split('&').reduce((p, part, index) => {
            if (index === 0) {
                return part + '?redirect';
            }
            return p + '&' + part;
        }, '');
    }
    return newPath;
};

const redirect = ({ path, history }) => {
    if (location.hash.startsWith('#state=')) {
        const url = `${location.pathname}?${location.hash.substring(1)}`;
        history.replace(url);
    } else if (path) {
        history.replace(fixRedirectPath(path));
    } else if (!location.pathname.substring(1)) {
        history.push('/home');
    }
};

const setGtag = (ga4react, kc) => {
    try {
        if (ga4react) {
            const { tokenParsed } = kc;
            const { name, group } = tokenParsed;
            let userType = 'Staff';
            if (group.some(item => item.includes('External'))) {
                userType = group.filter(item => item.includes('External'))[0].split('/')[2];
            } else if (includes(group, '/Department/IT/Admin')) {
                userType = 'IT Admin';
            }
            ga4react.initialize();
            ga4react.gtag('set', { userName: name });
            ga4react.gtag('set', { userType });
        }
    } catch (error) {
        console.log('Something went wrong in gtag:', error);
    }
};

auth.init()
    .then(({ keycloak: kc, authenticated }) => {
        const pathName = path ? path.split('?')[0] : location.pathname;

        // module that doesn't depends on authentication
        for (const router of publicRouters) {
            const urlRegx = pathToRegexp(router.path);
            if (urlRegx.exec(pathName)) {
                registerApplication(
                    router.module,
                    () => System.import(router.module),
                    router.activityFn,
                    {
                        history,
                        pathName,
                    },
                );
                start();
                console.log('start');
                redirect({ path, history });
                return;
            }
        }

        // module that depends on authentication
        if (authenticated) {
            notificationEvents.connect();
            notificationEvents.listen(message => {
                console.log('notificationEvents:', message);
            });
            const { GA_CODE } = process.env;
            const ga4react = new GA4React(toString(GA_CODE));
            setGtag(ga4react, kc);
            registerApplication(
                '@paris2/navbar',
                () => System.import('@paris2/navbar'),
                isActive.navbar,
                { kc, history, notificationEvents, isNovaActive: isActive.nova, ga4react },
            );
            registerApplication(
                '@paris2/notifications',
                () => System.import('@paris2/notifications'),
                isActive.notifications,
                { kc, history, notificationEvents },
            );
            registerApplication(
                '@paris2/vessel',
                () => System.import('@paris2/vessel'),
                isActive.vessel,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/seafarer',
                () => System.import('@paris2/seafarer'),
                isActive.seafarer,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/item-master',
                () => System.import('@paris2/item-master'),
                isActive.itemMaster,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/inspections-audits',
                () => System.import('@paris2/inspections-audits'),
                isActive.vesselInspectionReport,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/deficiency',
                () => System.import('@paris2/deficiency'),
                isActive.deficiency,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/quality-management-system',
                () => System.import('@paris2/quality-management-system'),
                isActive.qualityManagementSystem,
                { kc, notificationEvents },
            );
            registerApplication(
                '@paris2/ship-party',
                () => System.import('@paris2/ship-party'),
                isActive.shipParty,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/tableau',
                () => System.import('@paris2/tableau'),
                isActive.tableau,
                { kc, notificationEvents },
            );
            registerApplication(
                '@paris2/nova',
                () => System.import('@paris2/nova'),
                isActive.nova,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/landing-page',
                () => System.import('@paris2/landing-page'),
                isActive.landingPage,
                { kc, history, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/import-sheet',
                () => System.import('@paris2/import-sheet'),
                isActive.importsheet,
                { kc, history, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/external-reporting',
                () => System.import('@paris2/external-reporting'),
                isActive.externalReporting,
                { kc, history, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/survey',
                () => System.import('@paris2/survey'),
                isActive.seafarerSurvey,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/reference',
                () => System.import('@paris2/reference'),
                isActive.reference,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/qhse',
                () => System.import('@paris2/qhse'),
                isActive.qhse,
                { kc, notificationEvents, ga4react },
            );
            registerApplication('@paris2/pms', () => System.import('@paris2/pms'), isActive.pms, {
                kc,
                notificationEvents,
                ga4react,
            });
            registerApplication(
                '@paris2/owner-reporting',
                () => System.import('@paris2/owner-reporting'),
                isActive.ownerReporting,
                { kc, notificationEvents, ga4react },
            );
            registerApplication(
                '@paris2/risk-assessment',
                () => System.import('@paris2/risk-assessment'),
                isActive.riskAssessment,
                { kc, notificationEvents, ga4react },
            );
            start();
            console.log('start');
            redirect({ path, history });
        } else {
            kc.login();
        }
    })
    .catch(error => {
        console.log(error);
    });
