const path = require('path');

const { ENV = 'dev' } = process.env
const envPath = path.resolve(__dirname, '..', '..', 'paris2-configuration.env');
require('dotenv').config({ path: envPath });

const fs = require('fs');

var dir = './dist';

if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
}

const importMapPath = path.resolve(__dirname, '..', 'dist', ENV, 'importmap');

if (!fs.existsSync(importMapPath)){
    fs.mkdirSync(importMapPath);
}

const { SCRIPT_BASE_URL, NPM_CDN_PATH } = process.env;

const modules = require(path.resolve(__dirname, 'modules.json'));
const devLibraries = require(path.resolve(__dirname, 'libraries.dev.json'));
const libraries = require(path.resolve(__dirname, 'libraries.json'));

const timestamp = Date.now();

const spaModules = Object.keys(modules).map(key => ({
  key,
  path: SCRIPT_BASE_URL + modules[key] + '?t=' + timestamp,
})).reduce((map, { key, path }) => ({ ...map, [key]: path }), {})

const fullLibraries = Object.keys(libraries).reduce((map, key) => ({
  ...map, [key]: `${NPM_CDN_PATH}/${libraries[key]}`
}), {});

if (ENV === 'dev') {
  Object.keys(devLibraries).forEach((key) => {
    fullLibraries[key] = `${NPM_CDN_PATH}/${devLibraries[key]}`
  });
}

const importmap = {
  imports: {
    ...spaModules,
    ...fullLibraries,
  }
}

let data = JSON.stringify(importmap);
fs.writeFileSync(path.resolve(importMapPath, 'importmap.json'), data);