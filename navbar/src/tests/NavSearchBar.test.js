import React from "react";
import NavSearchBar from "../components/NavSearchBar.js";
import { render, screen } from "@testing-library/react";

jest.mock("../styleGuide");

describe("<NavSearchBar />", () => {
  beforeEach(() => {
    render(<NavSearchBar />);
  });

  it("should have search bar", () => {
    expect(screen.getByTestId("header-search")).toBeInTheDocument();
  });

  it("with search icon", () => {
    expect(screen.getByTestId("header-search-icon")).toBeInTheDocument();
  });

  it("and search box with placeholder 'Search'", () => {
    expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
  });
});
