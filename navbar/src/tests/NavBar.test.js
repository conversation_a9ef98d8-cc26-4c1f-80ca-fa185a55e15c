import React from "react";
import NavBar from "../components/NavBar.js";
import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { links } from "../modules.helper";
import "@testing-library/jest-dom";
jest.mock("../notification-events");
jest.mock("../styleGuide");

const env = process.env.ENV || "dev";

const kcMock = {
  tokenParsed: {
    name: "<PERSON>",
    rank: "Software Engineer"
  },
  realmAccess: {
    roles: ["vessel|view", "seafarer|view|general"]
  },
  logout: jest.fn()
};

const notificationEvents = {
  listen: () => jest.fn()
};

describe("<NavBar />", () => {
  beforeEach(() => {
    render(
      <MemoryRouter>
        <NavBar
          languages={[]}
          currentLanguage=""
          kc={kcMock}
          currentPath="/"
          notificationEvents={notificationEvents}
        />
      </MemoryRouter>
    );
  });

  it("should have a side bar", async () => {
    await waitFor(() => {
      expect(screen.getByTestId("sidebar-menu-icon")).toBeInTheDocument();
    });
  });

  it("should have circle icon with Paris brand name on home page", () => {
    const brandCircle = screen.getByTestId("header-brand-icon");
    const brandName = screen.getByText(links.home.title);
    expect(brandCircle).toBeInTheDocument();
    const expected =
      env.toUpperCase() === "UAT"
        ? `${links.home.title} (UAT)`
        : links.home.title;
    expect(brandName).toHaveTextContent(expected);
  });

  it("should have a brand name as Vessel when current route is with /vessel", () => {
    render(
      <MemoryRouter>
        <NavBar
          languages={[]}
          currentLanguage=""
          kc={kcMock}
          currentPath="/vessel/archived"
          notificationEvents={notificationEvents}
        />
      </MemoryRouter>
    );
    const expected = env.toUpperCase() === "UAT" ? "Vessel (UAT)" : "Vessel";
    expect(screen.getByText(expected)).toBeInTheDocument();
  });

  it("should have icon with module brand name on module page", () => {
    const brandName = screen.getByText("PARIS Home");
    const expected =
      env.toUpperCase() === "UAT"
        ? `${links.home.title} (UAT)`
        : links.home.title;
    expect(brandName).toHaveTextContent(expected);
  });

  it("should have NavRightGroup", () => {
    const container = document.body;
    expect(container.querySelector("#basic-navbar-nav")).toBeInTheDocument();
  });
});
