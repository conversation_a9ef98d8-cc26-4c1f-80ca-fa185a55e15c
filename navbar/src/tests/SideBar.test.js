import React from "react";
import SideBar, { getNavLinkClass, hasRole } from "../components/SideBar";
import { links } from "../modules.helper";
import { FaUserCog } from "react-icons/fa";
import { AccordionMenu } from "../components/AccordionMenu";
import styleGuide from "../styleGuide";
import {
  screen,
  render,
  fireEvent,
  waitForElementToBeRemoved,
  cleanup
} from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";

jest.mock("../styleGuide");

const { Icon } = styleGuide;

const kcMock = {
  tokenParsed: {
    name: "<PERSON>",
    rank: "Software Engineer"
  },
  realmAccess: {
    roles: [
      "vessel|view",
      "seafarer|view|general",
      "item-master|view",
      "ship-party|view",
      "nova|view",
      "vessel-inspection-report|view",
      "paris1links|view"
    ]
  },
  logout: jest.fn()
};

const kcOwnerMock = {
  tokenParsed: {
    name: "Kent Beck",
    rank: "Software Engineer",
    ship_party_id: 100,
    ship_party_type: "Owner"
  },
  realmAccess: {
    roles: ["vessel|view", "nova|view"]
  },
  logout: jest.fn()
};

const kcManningAgentMock = {
  tokenParsed: {
    name: "Manning Agent",
    rank: "crew agent",
    ship_party_id: 100,
    ship_party_type: "Manning Agency"
  },
  realmAccess: {
    roles: ["vessel|view", "nova|view"]
  },
  logout: jest.fn()
};

const kcTechgroupMemberMock = {
  tokenParsed: {
    name: "Techgroup User",
    rank: "Techgroup Member",
    group: ["/Tech Group/Bunker Tech"]
  },
  realmAccess: {
    roles: []
  },
  logout: jest.fn()
};

const getVesselTypeKcMemberRole = vesselTypeRole => ({
  tokenParsed: {
    name: "Techgroup User",
    rank: `Techgroup Manager ${vesselTypeRole}`,
    group: []
  },
  realmAccess: {
    roles: [vesselTypeRole]
  },
  logout: jest.fn()
});

let sideBar;
let sideBarUtils;
describe("<SideBar />", () => {
  beforeEach(() => {
    process.env = Object.assign(process.env, {
      PARIS_ONE_HOST: "https://parisdev.fleetship.com"
    });
    sideBarUtils = render(
      <MemoryRouter>
        <SideBar kc={kcMock} currentPath="/home" visible />
      </MemoryRouter>
    );
  });

  it("should have header", () => {
    expect(screen.getByTestId("sidebar-header")).toBeInTheDocument();
  });

  it("should have menu icon on top", () => {
    expect(screen.getByTestId("sidebar-menu-icon")).toBeInTheDocument();
  });

  it("should have owner sidebar modules", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcOwnerMock} currentPath="/home" visible />
      </MemoryRouter>
    );
    const actualLinks = screen.getAllByRole("link").map(link => {
      return [link.textContent, link.getAttribute("href")];
    });
    const vesselButton = screen.getByText("Vessel");
    expect(vesselButton).toBeInTheDocument();

    const expectedLinks = [[links.home.title, links.home.href]];
    expect(actualLinks).toEqual(expect.arrayContaining(expectedLinks));
  });

  it("should have non owner sidebar modules", () => {
    const actualLinks = screen.getAllByRole("link").map(link => {
      return [link.textContent, link.getAttribute("href")];
    });
    const expectedLinks = [[links.home.title, links.home.href]];

    expect(actualLinks).toEqual(expect.arrayContaining(expectedLinks));
    const vesselButton = screen.getByText("Vessel");
    expect(vesselButton).toBeInTheDocument();

    const seafarerButton = screen.getByText("Seafarer");
    expect(seafarerButton).toBeInTheDocument();
  });

  it("should have standard paris1 link if user is non owner", () => {
    const paris1Link = screen.getAllByRole("link").map(link => {
      return [link.textContent, link.getAttribute("href")];
    });
    const paris1Links = [
      ["PARIS 1.0", "https://parisdev.fleetship.com/fml/PARIS"]
    ];
    expect(paris1Link).toEqual(expect.arrayContaining(paris1Links));
  });

  it("should not have paris1 link if user is owner", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcOwnerMock} currentPath="/home" visible />
      </MemoryRouter>
    );
    const paris1Link = screen.queryByRole("button", {
      name: /paris 1\.0/i
    });
    expect(paris1Link).not.toBeInTheDocument();
  });

  it("should not have vessel module if user is owner", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcOwnerMock} currentPath="/home" visible />
      </MemoryRouter>
    );

    const actualLinks = screen.getAllByRole("link").map(link => {
      return [link.textContent, link.getAttribute("href")];
    });

    // Owner should not see vessel module
    const expectedLinks = [["Vessel", "/vessel"]];

    expect(actualLinks).toEqual(expect.not.arrayContaining(expectedLinks));
  });

  it("should have vessel module if user is non owner", () => {
    const vesselButton = screen.getAllByText("Vessel");
    expect(vesselButton).toHaveLength(1);
  });

  it("should have vessel module if user is Manning Agent", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcManningAgentMock} currentPath="/home" visible />
      </MemoryRouter>
    );
    const vesselButton = screen.getAllByText("Vessel");
    expect(vesselButton).toHaveLength(1);
  });

  it("should have admin module if user neither have item master or ship party roles for external reporting", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcOwnerMock} currentPath="/home" visible />
      </MemoryRouter>
    );
    expect(
      screen.queryByTestId("sidebar-navigation-administration")
    ).toBeInTheDocument();
    expect(
      screen.queryByText("Item Master")
    ).not.toBeInTheDocument();
  });

  it("should have no access to inspection/audit module if user doesn't have inspection role", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar kc={kcOwnerMock} currentPath="/home" visible />
      </MemoryRouter>
    );
    expect(
      screen.queryByTestId("sidebar-navigation-inspection")
    ).not.toBeInTheDocument();
  });

  it("should have access to admin module if user have item master view ", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar
          kc={{
            ...kcOwnerMock,
            realmAccess: {
              ...kcOwnerMock.realmAccess,
              roles: ["item-master|view"]
            }
          }}
          currentPath="/home"
          visible
        />
      </MemoryRouter>
    );
    expect(screen.getAllByText("Administration")).toHaveLength(1);
  });

  it("should have access to admin module if user have ship party view ", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <SideBar
          kc={{
            ...kcOwnerMock,
            realmAccess: {
              ...kcOwnerMock.realmAccess,
              roles: ["ship-party|view"]
            }
          }}
          currentPath="/home"
          visible
        />
      </MemoryRouter>
    );

    expect(screen.getAllByText("Administration")).toHaveLength(1);
  });

  it("should show non owner sidebar admin modules", () => {
    const kc = {
      ...kcOwnerMock,
      realmAccess: {
        ...kcOwnerMock.realmAccess,
        roles: ["ship-party|view", "item-master|view"]
      }
    };
    sideBarUtils.rerender(
      <MemoryRouter>
        <AccordionMenu
          id="administration"
          links={links.paris20_administration_modules.filter(hasRole(kc))}
          ParentIcon={
            <FaUserCog
              size={30}
              className="sidebar_navigation__icon administration-icon"
            />
          }
          title="Administration"
          getNavLinkClass={() => {}}
          onClick={() => {}}
          keycloak={kc}
          currentPath={"/home"}
        />
      </MemoryRouter>
    );
    fireEvent.click(screen.getByTestId("administration-sidebar-module"));
    const administrationModules = screen.getAllByRole("button").map(button => {
      return button.textContent;
    });

    const administrationModulesLinks = ["Item Master", "Ship Parties"];
    expect(administrationModules).toEqual(
      expect.arrayContaining(administrationModulesLinks)
    );
  });

  it("should show blue background on currently active menu item", () => {
    sideBarUtils.rerender(
      <MemoryRouter>
        <AccordionMenu
          key="vessel"
          id="vessel"
          links={links.paris20_vessel_modules.filter(hasRole(kcOwnerMock))}
          ParentIcon={
            <Icon
              icon={"vessel-invert"}
              size={30}
              className="sidebar_navigation__icon text-primary"
            />
          }
          title="Vessel"
          getNavLinkClass={getNavLinkClass}
          onClick={() => {}}
          keycloak={kcOwnerMock}
          currentPath="/vessel"
        />
      </MemoryRouter>
    );

    const activeItem = screen.getByRole("link", {
      current: "page"
    });
    expect(activeItem.textContent).toBe("Vessel List");
  });

  it("should change show state true, once the open button is clicked", () => {
    cleanup();
    render(
      <MemoryRouter>
        <SideBar kc={kcMock} currentPath="/home" visible={false} />
      </MemoryRouter>
    );

    const sideBarModel = screen.queryByTestId("sidebar-modal-layout");
    expect(sideBarModel).not.toBeInTheDocument();

    fireEvent.click(screen.getByTestId("sidebar-menu-icon"));
    expect(screen.getByTestId("sidebar-modal-layout")).toBeInTheDocument();
  });

  it("should change show state false, once the close button is clicked", async () => {
    fireEvent.click(screen.getByTestId("sidebar-title-menu-icon"));
    await waitForElementToBeRemoved(() =>
      screen.getByTestId("sidebar-modal-layout")
    );
    const sideModel = screen.queryByTestId("sidebar-modal-layout");
    expect(sideModel).not.toBeInTheDocument();
  });
});
