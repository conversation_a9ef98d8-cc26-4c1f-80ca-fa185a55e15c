import React, { useEffect, useReducer, useState } from "react";
import moment from "moment";
import { Button, Nav, Navbar } from "react-bootstrap";
import { Link } from "react-router-dom";
import NavRightGroup from "./NavRightGroup.js";
import SideBar from "./SideBar";
import { getAllModuleLinks, links } from "../modules.helper";
import styleGuide from "../styleGuide";
import {
  MESSAGE_STATUS_READ,
  NotificationEventListener
} from "../notification-events";

const { Icon } = styleGuide;

const notificationsReducer = (notifications, event) => {
  switch (event.type) {
    case "new": {
      return [...event.notifications, ...notifications].slice(0, 3);
    }
    case "update": {
      const index = notifications.findIndex(
        ({ id }) => id === event.notification.id
      );
      if (index !== -1) {
        return [
          ...notifications.slice(0, index),
          event.notification,
          ...notifications.slice(index + 1)
        ];
      }
      return notifications;
    }
    case "delete": {
      return notifications.filter(notification => notification.id != event.id);
    }
    case "deleteAll": {
      return [];
    }
  }
};

const mapNotification = ({
  id,
  status,
  message,
  module,
  created_at,
  link,
  icon
}) => {
  return {
    id,
    icon,
    text: message,
    module: module || "general",
    date: moment(created_at).fromNow(),
    status: status === MESSAGE_STATUS_READ ? "read" : "new",
    href: link || ""
  };
};

const NavBar = ({
  languages,
  currentLanguage,
  kc,
  currentPath,
  isGradientBackground,
  ga4react,
  notificationEvents
}) => {
  const { ENV } = process.env;
  const [brandName, setBrandName] = useState(null);
  const [href, setHref] = useState("/home");
  const [icon, setIcon] = useState(null);
  const [notifications, dispatchNotifications] = useReducer(
    notificationsReducer,
    []
  );
  const [hasUncheckedMessage, setHasUncheckedMessage] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, label, category, false);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    setNavbarTitleBasedOnCurrentPath();
  });

  useEffect(() => {
    const closeListener = notificationEvents.listen(
      new NotificationEventListener({
        onMessages: messages => {
          dispatchNotifications({
            type: "new",
            notifications: messages.map(mapNotification)
          });
        },
        onUser: user => {
          setHasUncheckedMessage(user.uncheckedMessageCount > 0);
          setUnreadCount(user.totalUnreadMessageCount);
        },
        onMessageUpdate: message => {
          dispatchNotifications({
            type: "update",
            notification: mapNotification(message)
          });
        },
        onMessageRemove: messageId => {
          dispatchNotifications({
            type: "delete",
            id: messageId
          });
        },
        onAllMessageRemove: () => {
          dispatchNotifications({ type: "deleteAll" });
        }
      })
    );
    return () => closeListener();
  }, []);

  const onNotificationButtonClick = () => {
    ga4EventTrigger("Notification", "Nav Bar", "Notification");
    notificationEvents.resetUncheckedMessageCount();
  };

  const defaultBrandName = "PARIS 2.0";

  const getBrandName = path => {
    return path && path.startsWith("/vessel") ? "Vessel" : defaultBrandName;
  };

  useEffect(() => {
    setBrandName(getBrandName());
  }, []);

  const setNavbarTitleBasedOnCurrentPath = () => {
    const allLinks = getAllModuleLinks();
    const matchedLinks = allLinks.filter(link =>
      currentPath.startsWith(`/${link.href.split("/")[1]}`)
    );
    if (matchedLinks.length > 0) {
      if (ENV === "uat") setBrandName(`${matchedLinks[0].title} (UAT)`);
      else setBrandName(matchedLinks[0].title);
      setIcon(matchedLinks[0].icon);
    } else {
      if (ENV === "uat") setBrandName(`${links.home.title} (UAT)`);
      else setBrandName(links.home.title);
      setIcon(links.home.icon);
    }
  };

  const onNotificationItemClick = messageId =>
    notificationEvents.markMessageRead(messageId);

  return (
    <Navbar
      variant="dark"
      expand="sm"
      fixed="top"
      className={`nav-bg-color`}
    >
      <SideBar
        kc={kc}
        currentPath={currentPath}
        ga4EventTrigger={ga4EventTrigger}
      />
      <Navbar.Brand>
        <Link
          onClick={() => ga4EventTrigger("home", "Nav", currentPath)}
          to={href}
        >
          <Button block className="no-fill brand-color">
            {icon && (
              <Icon
                data-testid="header-brand-icon"
                icon={icon}
                color={"white"}
                size={30}
                className="header__circle"
              />
            )}
            {brandName}
          </Button>
        </Link>
      </Navbar.Brand>
      <Navbar.Toggle aria-controls="basic-navbar-nav" />
      <Navbar.Collapse id="basic-navbar-nav">
        <Nav className="container-fluid ml-auto">{/*<NavSearchBar />*/}</Nav>
        <NavRightGroup
          kc={kc}
          currentPath={currentPath}
          notifications={notifications}
          hasUncheckedMessage={hasUncheckedMessage}
          unreadCount={unreadCount}
          onNotificationButtonClick={onNotificationButtonClick}
          isNovaActive={isGradientBackground}
          onNotificationItemClick={onNotificationItemClick}
          ga4EventTrigger={ga4EventTrigger}
        />
      </Navbar.Collapse>
    </Navbar>
  );
};
export default NavBar;
