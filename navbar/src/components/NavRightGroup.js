import React, { useRef } from "react";
import {
  Nav,
  NavDropdown,
  Button,
  OverlayTrigger,
  Popover,
  NavItem
} from "react-bootstrap";
import { Link, NavLink } from "react-router-dom";
import styleGuide from "../styleGuide";
import { FAQ_PATH_MODULE } from "../constants/faq_path";
const { Icon } = styleGuide;

const { PARIS_TWO_URL } = process.env;

const getFaqModulePath = moduleName => {
  if (!FAQ_PATH_MODULE[moduleName]) return FAQ_PATH_MODULE.home;
  return FAQ_PATH_MODULE[moduleName];
};

const NavRightGroup = ({
  kc,
  notifications,
  unreadCount = 0,
  hasUncheckedMessage,
  onNotificationButtonClick,
  onNotificationItemClick,
  customBackgroundClass,
  isNovaActive,
  currentPath,
  ga4EventTrigger = () => {}
}) => {
  const name = kc.tokenParsed ? kc.tokenParsed.name : "";
  const rank = kc.tokenParsed ? kc.tokenParsed.rank : "";
  const department = kc.tokenParsed ? kc.tokenParsed.department : "";
  const userInitials = getInitials(name);
  const placement = "bottom";
  const unreadCountText = unreadCount > 0 ? ` (${unreadCount})` : "";
  const notificationRef = useRef(null);
  const faqRef = useRef(null);

  const logout = () => {
    let redirectUri = location.href;
    ga4EventTrigger("Sign Out", "Nav Bar", "Sign Out");
    if (!redirectUri.includes("?")) {
      redirectUri = `${location.href}?redirect`;
    }
    window.dispatchEvent( new Event('logout') );
    kc.logout({
      redirectUri
    });
  };

  const handleClickFAQ = () => {
    ga4EventTrigger("FAQ", "Nav Bar", "FAQ");
    const moduleName = currentPath
      .split("/")
      .filter(Boolean)
      .shift();
    const faqModulePath = getFaqModulePath(moduleName);
    window.open(`${PARIS_TWO_URL}/${faqModulePath}`, "_blank");
    return;
  };

  return (
    <Nav>
      <OverlayTrigger
        rootClose={true}
        trigger="click"
        key="bottom"
        placement="bottom"
        overlay={
          <Popover
            id={`popover-positioned-bottom`}
            className="notification_popover"
          >
            <Popover.Title as="h4">
              Notifications{unreadCountText}
            </Popover.Title>
            <Popover.Content>
              {notifications.map(notification => {
                const onItemClick = () =>
                  onNotificationItemClick(notification.id);
                const isExternalLink = notification.href?.startsWith("http");
                const innerComponents = (
                  <>
                    <Icon
                      className="notif-icon"
                      icon={notification.icon}
                      size={30}
                    />
                    <span className="content">
                      {formatMessage(notification.text)}
                    </span>
                    <span className="module">{notification.module}</span>
                    <span className="date">{notification.date}</span>
                  </>
                );
                return isExternalLink ? (
                  <a
                    key={notification.id}
                    href={notification.href}
                    target="_blank"
                    className={
                      "notification_popover__notification " +
                      notification.status
                    }
                    onClick={onItemClick}
                  >
                    {innerComponents}
                  </a>
                ) : (
                  <Link
                    key={notification.id}
                    to={notification.href}
                    className={
                      "notification_popover__notification " +
                      notification.status
                    }
                    onClick={onItemClick}
                  >
                    {innerComponents}
                  </Link>
                );
              })}
              <Link
                className="notification_popover__footer"
                to="/notifications"
              >
                <h4><u>See more notifications</u></h4>
              </Link>
            </Popover.Content>
          </Popover>
        }
      >

        <button variant="primary" className={`header__right_group__notification ${customBackgroundClass} btn`} 
            onClick={onNotificationButtonClick}  
            id='notification-card' 
            ref={notificationRef}>
            
          <Icon
            icon={hasUncheckedMessage ? "notification-on" : "notification-off"}
            size={25}
            data-testid="test__link-notification"
            id="test__link-notification"
          />
        </button>
      </OverlayTrigger>
      <button
        variant="primary"
        onClick={handleClickFAQ}
        target="_blank"
        className={`${customBackgroundClass} btn`}
        data-testid="faq-card"
        id="faq-card"
        ref={faqRef}
      >
        <Icon icon="help" size={18} />
      </button>
      <NavItem
        as={NavLink}
        href="#"
        className="header__right_group"
        data-testid="navbar-right-user"
      >
        <NavDropdown
          onToggle={isOpen =>
            isOpen && ga4EventTrigger("Profile", "Nav Bar", "Profile")
          }
          className="header__right_group__link"
          title={userInitials}
        >
          <NavDropdown.Item>
            <div
              className="header__right_group__avatar-circle"
              data-testid="header-avatar-icon"
            >
              {userInitials}
            </div>
          </NavDropdown.Item>
          <Nav className="header__right_group__name">{name}</Nav>
          <Nav className="header__right_group__title">{rank}</Nav>
          <Nav className="header__right_group__department">{department}</Nav>
          <Nav className="header__right_group__account-details">
            Account Details
          </Nav>
          <NavDropdown.Item>
            <Button
              className="btn-logout sign-out-button"
              onClick={logout}
              data-testid="sign-out-button"
            >
              Sign out
            </Button>
          </NavDropdown.Item>
        </NavDropdown>
      </NavItem>
    </Nav>
  );
};

export default NavRightGroup;

function getInitials(name) {
  return name.charAt(0).toUpperCase();
}

function formatMessage(message) {
  const statusProps = [
    {
      value: "reapplied",
      color: "#1F4A70" // navy blue
    },
    {
      value: "forwarded",
      color: "#1F4A70" // navy blue
    },
    {
      value: "approved",
      color: "#28A747" // green
    },
    {
      value: "reviewed",
      color: "#28A747" // green
    },
    {
      value: "rejected",
      color: "#D41B56" // red
    },
    {
      value: "pending",
      color: "#FFC107" // orange
    },
    // Data Import / item-master
    {
      value: "successfully",
      color: "#28A747" // green
    },
    {
      value: "Started",
      color: "#FFC107" // orange
    },
    {
      value: "Failed",
      color: "#D41B56" // red
    }
  ];
  const words = message.split(" ");
  return words.reduce((wordArr, word, index) => {
    const foundStatus = statusProps.find(
      statusProp => statusProp.value === word
    );
    return [
      ...wordArr,
      index === 0 ? null : " ",
      foundStatus ? (
        <span key={index} style={{ color: foundStatus.color, fontWeight: 600 }}>
          {foundStatus.value}
        </span>
      ) : (
        word
      )
    ];
  }, []);
}
