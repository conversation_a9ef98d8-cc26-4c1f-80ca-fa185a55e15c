import React from "react";
import { Form, FormControl, InputGroup } from "react-bootstrap";
import styleGuide from "../styleGuide";
const { Icon } = styleGuide;

const NavSearchBar = () => {
  return (
    <Form className="header__search" data-testid="header-search">
      <InputGroup id="search">
        <InputGroup.Prepend>
          <span className="input-group-text">
            <Icon
              icon="search"
              color={"white"}
              size={25}
              data-testid="header-search-icon"
            />
          </span>
        </InputGroup.Prepend>
        <FormControl
          className="header__search__inputfield"
          type="search"
          placeholder="Search"
          aria-describedby="icon-search"
        />
      </InputGroup>
    </Form>
  );
};

export default NavSearchBar;
