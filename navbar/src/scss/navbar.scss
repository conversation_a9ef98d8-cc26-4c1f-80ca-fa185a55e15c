@import 'mixins';

$dark-blue: #1C1F4A;
$grey: #343A40;
$white: #fff;
$icon-hover-color: #0E1025;


nav {
    font-weight: 600;
    .btn {
        display: inline-block;
        font-weight: 400;
        color: $white;
        text-align: center;
        vertical-align: middle;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }
    .btn:hover {
        background-color:$icon-hover-color;
        border-color: $icon-hover-color;
        color: $white;
    }

    .btn:focus {
        background-color:$icon-hover-color;
        border-color: $icon-hover-color;
        box-shadow:unset;
    }

    .btn:active {
        background-color:$icon-hover-color;
        border-color: $icon-hover-color;
        color: $white;
    }

}

a {
    text-decoration: none!important;
}

.header {
  &__btn {
    margin: 0;
    padding: 0;
    text-align: left;
    color: white;
    background-color: $dark-blue;
    border-color: $dark-blue;
    font-size: 1.25rem;
    &:active, &:hover, &:focus, &:visited {
        color: white!important;
        background-color: $dark-blue !important;
        border-color: $dark-blue !important;
    }
  }
  &__circle {
    margin-top: -5px;
    margin-right: 10px;
  }
  &__search {
    width: 400px;
    height: 32px;
    background: $grey 0% 0% no-repeat padding-box;
    border-radius: 4px;
    opacity: 1;
    margin: auto;
    &__inputfield {
      width: 380;
      height: 32px;
      background: transparent 0% 0% no-repeat padding-box;
      border: None;
      text-align: left;
      letter-spacing: 0px;
      color: white;
      opacity: 1;
      font-size: 17px;
      font-weight: 400;
      &:focus {
        background: transparent 0% 0% no-repeat padding-box;
        border:None;
        border-radius: None;
        color: white;
        box-shadow: None;
      }
    }
    .input-group-text {
      background-color: $grey;
      border: None;
      height: 32px;
    }
  }
  &__right_group {
    .dropdown-toggle.nav-link{
      padding-top: 3px;
      font-size: 17px;
      @include name-circle(30px);

      &:hover, &:active, &:focus {
        color: $dark-blue!important
      }
    }::after {
      content: none;
    }

    .dropdown-menu {
      width: 300px;
      padding: 0.5rem;
      background: $dark-blue 0% 0% no-repeat padding-box;
      box-shadow: 0px 2px 4px #00000080;
      right: -17px;
      left: auto;
      text-align: center;
      .dropdown-item:hover {
        background-color: inherit;
      }
    }

    &__notification {
        svg > path:nth-child(2) {
            fill: red;
            stroke:white;
            stroke-width: 50;
        }
    }
    &__link {
      padding: 8px;
    }

    &__avatar-circle {
      margin-left: 95px;
      padding-top: 5px;
      margin-top: 30px;
      margin-bottom: 10px;
      font-size: 34px;
      @include name-circle(60px);
    }

    &__name {
      color: $white;
      font-weight: 600;
      font-size: 17px;
      margin-bottom: -3px;
      display: block;
    }
    &__title {
      color: $white;
      font-weight: 400;
      font-size: 14px;
      display: block;
    }
    &__department {
      color: $white;
      font-weight: 400;
      font-size: 14px;
      display: block;
    }
    &__account-details {
      color: $white;
      margin: 18px 20px;
      font-weight: 500;
      display: block;
    }
  }
}

.btn-logout {
  background-color: $dark-blue;
  border-color: white !important;
  &:hover, &:active, &:visited, &:focus {
    background-color: white !important;
    border-color: $dark-blue;
    color: $dark-blue !important;
  }
}

.button-help {
    padding-bottom: 10px;
    margin-left: 4px;
}

span.main-menu__icon {
    margin-right: 20px;
    cursor: pointer;
    margin-top: -5px;
}
span.main-menu__icon__subnav {
    cursor: pointer;
    margin-top: -10px;
    color: $dark-blue !important;
}

.sideBar__modal {
    @media (max-width: 576px){
        .modal-dialog.modal-dialog-slideout {
            width: 80%
        }
    }
    & .modal-header {
        height: 62px;
        border-bottom: 1px solid $dark-blue !important;
    }
    & .modal-title {
        line-height: initial;
    }
    & .modal-dialog {
        width: 385px;
        height: 100%!important;
        margin: 0 auto 0 0 ;
        background: white;
    }
    &.modal.fade .modal-dialog {
        -webkit-transform: translate(-100%, 0);
        transform: translate(-100%, 0);
    }
    &.modal.fade.show .modal-dialog {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
        flex-flow: column;
    }
    & .modal-content{
        border: 0;
    }
    .sidebar_navigation__icon {
        display: inline-block;
        margin: -2px 6px 0 0;
        &__article {
            margin-top: -5px;
        }
        &__close {
            cursor: pointer;
        }
        background-color: #fff;
        border-radius: 30px;
    }
    .btn-sidenav__module {
      margin: 5px 0;
      padding: 6px;
      text-align: left;
      color: $dark-blue;
      background-color: inherit;
      font: normal normal bold 14px/17px Inter,sans-serif;
      font-weight: 600;
      &:active, &:focus{
        border: 0;
        background-color: #fff !important;
        box-shadow: none !important;
      }
    }

    .sidenav_btn {
      margin: 5px 0;
      padding: 6px 21px;
      text-align: left;
      color: $dark-blue;
      background-color: inherit;
      font: normal normal bold 14px/17px Inter,sans-serif;
      font-weight: 600; 
      border: 0;
      display: inline-flex;
      justify-content: space-between;
      &:active, &:focus{
        border: 0;
        background-color: #fff !important;
        box-shadow: none !important;
      }
    }

    .static_sidenav_btn {
        text-align: left;
        color: $dark-blue;
        background-color: inherit;
        font: normal normal bold 14px/17px Inter,sans-serif;
        border: 0;
        display: inline-flex;
        justify-content: space-between;
        &:active, &:focus{
          border: 0;
          background-color: $white !important;
          box-shadow: none !important;
        }
      }

    .sidenav_module {
      padding: 0.75rem;
      padding-left: 3rem;
      text-align: left;
      color: $dark-blue;
      background-color: inherit;
      font-family: Inter,sans-serif;
      font-size: 14px;
      font-weight: 500;
      border: 0;
      &:active, &:focus{
        border: 0;
        box-shadow: none !important;
      }
  }
    .side-nav-link {
      &:active, &:visited, &:focus{
        border: 0;
      color: $grey;
      background-color: #fff;
      }
    }
    .btn-sidenav__page {
        margin: 5px 0;
        padding: 6px;
        text-align: left;
        color: $grey;
        background-color: #efeded;;
        border-color: #efeded;;
        &:active, &:hover, &:focus, &:visited {
            color: $grey!important;
            background-color: #efeded!important;
            border-color: #efeded!important;
        }
    }
    h5 {
        display: inline-block;
        padding: 10px 0 0 8px;
        font-family: Inter,sans-serif;
        font-size: 17px;
        font-weight: 400;
        color: $grey;
        opacity: 1;
    }
    .active-link {
      background-color: #F2F7FF;
  }
}

// These should move over to the style-guide overrides
.popover {
    max-width: 375px!important;
}
.popover-header {
    padding: 1rem 0.75rem;
    margin-bottom: 0;
    font-size: 1rem;
    background-color: $white;
    color: $dark-blue;
}
.popover-body {
    padding: 0!important;
}

.notification_popover {
    h4 {
        text-align: center;
    }
    .new {
        background-color: #F2F7FF;
    }
}

.notification_popover .popover-body {
  max-height: 50vh;
  overflow-y: auto;
  white-space:pre-wrap;
}

a.notification_popover__notification {
    text-decoration: none;
    color: $dark-blue;
    width: 100%;
    display: grid;
    grid-template-columns: 10% auto auto auto;
    grid-template-rows: auto;
    grid-template-areas:
      "notif-icon content content content"
      ". module . date";
    gap: 10px;
    flex-wrap: wrap;
    padding: 15px;
    border-bottom: 1px solid #ebebeb;

    .content {
      grid-area: content;
    }

    .notif-icon {
      grid-area: notif-icon;
    }

    .module {
      grid-area: module;
      font-size: 12px;
    }
    .date {
      grid-area: date;
      font-size: 12px;
    }
}
a.notification_popover__footer{
    text-decoration: none;
    color: #052BC2;
    h4 {
        margin-top: 0.1rem;
    }
    
    u {
        font-size: 14px;
        text-align: center;
        line-height: 16px;
    }
}

.gradient-background {
  background: transparent linear-gradient(270deg, #0091B8 0%, #1F4A70 100%) 0% 0% no-repeat padding-box;
}

.no-fill {
  border: none !important;
  background: none !important;
}

.brand-color {
  color: #fff !important;
}

.sidenav-modal-body {
  padding: 0;
  margin-top: -1px;
}

.sidenav-menu-item {
  border-top: 0px solid $dark-blue;
  border-bottom: 1px solid $dark-blue;
  margin-bottom: -1px;
  margin-top: 1px;

  .text-primary{
    color: $dark-blue !important;
  }
}

.vessel-icon {
    color: #fff !important;
    background-color: $dark-blue !important;
    path {
        -webkit-transform: scale(0.7);
    transform: scale(0.7);
    transform-origin: center;
    }
}

.administration-icon {
  color: #fff !important;
  background-color: $dark-blue !important;
  path {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    transform-origin: center;
  }
}

.finance-icon {
    color: #fff !important;
    background-color: $dark-blue !important;
    path {
      -webkit-transform: scale(0.6);
      transform: scale(0.6);
      transform-origin: center;
    }
  }

.sidebar-sub-menu {
  font: normal normal medium 14px/16px Inter,sans-serif;
  letter-spacing: 0px;
  color: $dark-blue;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-top: 1px solid #DEE2E6;
}

.display-inline-grid {
  display: inline-grid;
}

.sidenav-non-owner-border {
  border-top : 2px solid #fff
}

.analytics-icon {
  background-color: $dark-blue !important;
}

.analytics-icon path{
  -webkit-transform: scale(0.6) translateX(-4%);
  transform: scale(0.6) translateX(-4%);
  transform-origin: center;
}

.nova-data-insight{
  display: inline-block;
  vertical-align: middle;
}

.nav-bg-color {
    background-color:$dark-blue;
}

.sign-out-button {
    font-size: 14px !important;
    line-height: 20px !important;
}


.navbar-nav {
    word-wrap: break-word;
}