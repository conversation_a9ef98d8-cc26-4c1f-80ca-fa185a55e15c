export const links = {
  home: {
    title: "PARIS Home",
    href: "/home",
    icon: "home-invert"
  },
  paris20_modules_in_development: [
    {
      title: "Quality Management System",
      href: "/quality-management-system",
      role: "quality-management-system|view"
    },
    {
      title: "NOVA",
      href: "/nova",
      role: "nova|view"
    },
    {
      title: "Tableau",
      href: "/tableau",
      role: "tableau|view"
    }
  ],
  paris20_modules: [
    {
      title: "World Map",
      href: "/worldmap"
    },
    {
      title: "Vessel",
      href: "/vessel",
      role: "vessel|view"
    },
    {
      title: "Seafarer",
      href: "/seafarer",
      role: "seafarer|view|general"
    },
    {
      title: "Ship Parties",
      href: "/ship-party",
      role: ["ship-party|view", "sp|ta|m", "sf|su"]
    },
    {
      title: "Item Master",
      href: "/item-master",
      role: "item-master|view"
    },
    {
      title: "External Reporting",
      href: "/external-reporting",
      role: "*"
    },
    {
      title: "Finance",
      href: "/portage-bill",
      role: "pb|v"
    },
    {
      title: "Survey",
      href: "/survey",
      role: [
        "survey|edit|crew-survey",
        "survey|view|supt-appraisal",
        "survey|view|debriefing"
      ]
    },
    {
      title: "QHSE",
      href: "/qhse",
      role: [
        "qhse|incident|view",
        "qhse|incident|manage",
        "qhse|near-miss|view",
        "qhse|near-miss|manage",
        "qhse|best-practice|view",
        "tmpl|vw",
        "ra|view",
        "ra|master|vw",
        "ra|admin|all" 
      ]
    },
    {
      title: "File Transfer",
      href: "/file-transfer"
    },
    {
      title: "Vessel",
      href: "/owner-reporting",
      role: "vessel|financial|view"
    },
    {
      title: "QHSE",
      href: "/risk-assessment",
      role: ["tmpl|vw", "ra|view","ra|master|vw", "ra|admin|all"]
    },
    {
      title: "Reference",
      href: "/reference",
      role: ["vessel|admin"]
    }
  ].filter(Boolean),
  paris20_worldmap_modules: {
    title: "World Map",
    href: "/worldmap",
    icon: "world-map"
  },
  paris20_seafarer_modules: [
    {
      title: "Seafarer List",
      href: "/seafarer/passed",
      role: "seafarer|view|general"
    },
    {
      title: "Seafarer Reports",
      href: "/seafarer-reports",
      role: ["sf|rt|md|v", "sf|rt|ap|v", "sf|rt|so|v", "sf|rt|dg|v", "sf|r4", "sf|r4|v", "sf|su"]
    },
    {
      title: "Crew Planner",
      href: "/seafarer/crew-planner/planner",
      role: ["sf|cp|pm|viewonly", "sf|cp|e"]
    },
    {
      title: "Crew Planner (Vessel)",
      href: "/seafarer/crew-planner/managed-vessel",
      role: ["sf|cp|pm|viewonly", "sf|pm|e"]
    },
    {
      title: "Admin Access",
      href: "/seafarer/crew-planner/manage-access",
      role: "sf|cp|m"
    },
    {
      title: "Daily News",
      href: "/seafarer/daily-news",
      role: "news|e"
    }
  ].filter(Boolean),
  paris20_pms_modules: [
    {
      title: "Master Data",
      href: "/pms/master-data"
    }
  ].filter(Boolean),
  paris20_survey_modules: {
    title: "Survey",
    href: "/survey",
    icon: "Survey-invert",
    role: [
      "survey|edit|crew-survey",
      "survey|view|supt-appraisal",
      "survey|view|debriefing"
    ]
  },
  paris20_reference_modules: {
    title: "Reference",
    href: "/reference",
    icon: "administration",
    role: ["general|view|reference"]
  },
  paris20_vessel_modules: [
    {
      title: "Vessel List",
      href: "/vessel",
      role: ["vessel|view", "vessel|view|dry", "vessel|view|tanker"]
    },
    {
      title: "Technical Reports",
      href: "/vessel/report/technical",
      role: ["vessel|view", "vessel|view|dry", "vessel|view|tanker"]
    },
    {
      title: "Environmental Reports",
      href: "/vessel/report/environmental",
      role: ["vessel|view", "vessel|view|dry", "vessel|view|tanker"]
    },
    {
      title: "Financial Reports Library",
      href: "/vessel/report/financial/accounts",
      role: ["vessel|financial|view"]
    },
    {
      title: "Cash Call Reports",
      href: "/vessel/report/financial/cash-call",
      role: ["vessel|cashcall|view"]
    },
    {
      title: "Owner Reports",
      href: "/vessel/report/financial/technical",
      role: ["vessel|ownerreport|view"]
    },
    {
      title: "Admin",
      href: "/vessel/admin",
      role: "vessel|admin"
    },
    {
      title: "Master Data",
      href: "/pms"
    }
  ].filter(Boolean),
  paris20_administration_modules: [
    {
      title: "Item Master",
      href: "/item-master",
      role: "item-master|view"
    },
    {
      title: "Ship Parties",
      href: "/ship-party",
      role: ["ship-party|view", "sp|ta|m", "sf|su"]
    },
    {
      title: "API Keys",
      href: "/ship-party/api-keys",
      role: "third-party|api-key|view"
    },
    {
      title: "External Reporting",
      href: "/external-reporting",
      role: "*"
    }
  ].filter(Boolean),
  paris20_file_transfer_modules: [
    {
      title: "Upload & Send",
      href: "/file-transfer/upload"
    },
    {
      title: "Request",
      href: "/file-transfer/upload-request"
    }
  ].filter(Boolean),
  paris20_inspection_audit_modules: [
    {
      title: "Reports",
      href: "/inspections-audits",
      role: "vessel-inspection-report|view"
    },
    {
      title: "Deficiencies",
      href: "/deficiency",
      role: "vessel-inspection-report|view"
    }
  ].filter(Boolean),
  paris20_nova_modules: [
    {
      title: "Owner Dashboard",
      href: "/nova",
      role: "nova|view"
    },
    {
      title: "ESG Analytics",
      href: "/dashboard/esg",
      role: "anl|in|v"
    },
    {
      title: "Procurement Analytics",
      href: "/dashboard/procurement",
      role: "anl|pcm|v"
    },
    {
      title: "Finance Invoice Analytics",
      href: "/dashboard/finance-invoice",
      role: "dap|fi|v"
    },
    {
      title: "KPI Scorecard Analytics",
      href: "/dashboard/kpi-scorecard",
      role: "anl|kpi|v"
    },
    {
      title: "Seafarer KPI Analytics",
      href: "/dashboard/seafarer-kpi-analytics",
      role: "anl|sf|v"
    },
    {
      title: "Customers KPI Analytics",
      href: "/dashboard/customer-kpi-analytics",
      role: "anl|cus|v"
    },
    {
      title: "Safeview Analytics",
      href: "/dashboard/safeview",
      role: [
        "anl|sv|v",
        "anl|sv|vd",
        "anl|sv|vw"
      ]
    },
    {
      title: "Incident Analytics",
      href: "/dashboard/incident-analytics",
      role: "anl|incd|v"
    }
  ].filter(Boolean),
  paris20_finance_modules: [
    {
      title: "Import",
      href: "/portage-bill",
      role: ["pb|v", "pb|e"]
    },
    {
      title: "Portage Bill",
      href: "/portage-bill/home",
      role: ["pb|v", "pb|e"]
    },
    {
      title: "Seafarer Account",
      href: "/portage-bill/seafarer-accounts",
      role: ["pb|v", "pb|e"]
    },
    {
      title: "Financial Report",
      href: "/portage-bill/financial-reports/allotment-summary",
      role: ["pb|v", "pb|e"]
    },
    {
      title: "Admin",
      href: "/portage-bill/admin",
      role: ["pb|v", "pb|e"]
    }
  ].filter(Boolean),
  paris20_qhse_modules: [
    {
      title: "Incident Reports",
      href: "/qhse/incident",
      role: ["qhse|incident|view", "qhse|incident|manage"]
    },
    {
      title: "Near Miss Reports",
      href: "/qhse/near-miss",
      role: ["qhse|near-miss|view", "qhse|near-miss|manage"]
    },
    {
      title: "Best Practice",
      href: "/qhse/best-practices",
      role: ["qhse|best-practice|manage", "qhse|best-practice|view"]
    },
    {
      title: "Risk Management",
      href: "/risk-assessment",
      role: ["tmpl|vw", "ra|view","ra|master|vw", "ra|admin|all"]
    } 
  ].filter(Boolean)
};

export const getAllModuleLinks = () =>
  links.paris20_modules_in_development.concat(links.paris20_modules);

export default {
  links,
  getAllModuleLinks
};
