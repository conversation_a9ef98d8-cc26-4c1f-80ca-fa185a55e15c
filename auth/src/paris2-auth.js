import Keycloak from "keycloak-js";
import "./set-public-path";

const { NODE_ENV, REALM, AUTH_CLIENT_ID } = process.env;
console.log("NODE_ENV:", NODE_ENV);

const isDev = NODE_ENV === "development";

const { AUTH_SERVER_URL } = process.env;

const TOKEN_MIN_VALIDITY = 5;

const keycloakConfig = {
  url: AUTH_SERVER_URL,
  realm: REALM,
  clientId: AUTH_CLIENT_ID
};

if (keycloakConfig.url.startsWith(":")) {
  keycloakConfig.url = `${window.location.protocol}//${window.location.hostname}${AUTH_SERVER_URL}`;
}

const keycloak = new Keycloak(keycloakConfig);

export const onAuthSuccessListeners = [];
keycloak.onAuthSuccess = () =>
  onAuthSuccessListeners.forEach(listener => listener());

keycloak.onTokenExpired = async () => {
  const refreshed = await keycloak.updateToken(TOKEN_MIN_VALIDITY);
  console.log("token expired, refreshed token:" + refreshed);
};

const getRedirectUri = () => {
  let redirectUri = location.href;
  if (!redirectUri.includes("?")) {
    redirectUri = `${location.href}?redirect`;
  }
  return redirectUri;
};

keycloak.onAuthRefreshError = () => {
  console.log("failed to refresh token. login again now.");
  keycloak.login({
    redirectUri: getRedirectUri()
  });
};

const addAuthListener = callback => onAuthSuccessListeners.push(callback);
const removeAuthListener = callback => {
  const index = onAuthSuccessListeners.indexOf(callback);
  if (index === -1) return;
  onAuthSuccessListeners.splice(index, 1);
};

/**
 * Solution of P2-13091:
 * The fix is from https://github.com/keycloak/keycloak/issues/14742
 * the problem is that react-dom behavior is having conflicts with the iframe
 * which checking the login in older version of chrome
 * Therefore the fix is using query string instead of fragments to reduce page render
 * and remove check-sso to remove iframe to check the page constantly
 **/
export const init = async () => {
  const authenticated = await keycloak.init({
    // onLoad: 'check-sso',
    promiseType: "native",
    // silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
    responseMode: "query"
  });

  return {
    keycloak,
    authenticated
  };
};

export const getToken = async () => {
  const token = keycloak.token;
  if (!token) {
    keycloak.login({
      redirectUri: getRedirectUri()
    });
    return null;
  }
  const refreshed = await keycloak.updateToken(TOKEN_MIN_VALIDITY);
  console.log("token refreshed:" + refreshed);
  return keycloak.token;
};

export default {
  init,
  getToken,
  keycloak
};
