/* eslint-disable no-undef */
const path = require('path');
const webpack = require('webpack');

const { ENV = 'dev' } = process.env
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });

const { CleanWebpackPlugin } = require('clean-webpack-plugin');

const { AUTH_SERVER_URL, REALM, AUTH_CLIENT_ID } = process.env;

module.exports = env => ({
    entry: path.resolve(__dirname, 'src/paris2-auth'),
    output: {
        filename: 'paris2-auth.js',
        libraryTarget: 'system',
        path: path.resolve(__dirname, 'dist', ENV)
    },
    devtool: 'source-map',
    module: {
        rules: [
            { parser: { system: false } },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: [{ loader: 'babel-loader' }],
            },
        ],
    },
    devServer: {
        historyApiFallback: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        allowedHosts: 'all',
        port: 9015
    },
    plugins: [
        new CleanWebpackPlugin(),
        new webpack.DefinePlugin({
          "process.env": {
            "AUTH_SERVER_URL": JSON.stringify(AUTH_SERVER_URL || ""),
            "REALM": JSON.stringify(REALM || ""),
            "AUTH_CLIENT_ID": JSON.stringify(AUTH_CLIENT_ID || "")
          }
        }),
    ],
    externals: [
        'single-spa', 
        /^@paris2\/.+$/, 
        // /^styled-components\/?.*$/,
        // /^react-i18next\/?.*$/, 
        // /^react-dom\/?.*$/,
        // /^react-bootstrap\/?.*$/,
        // /^react\/lib.*/,
        // /^axios\/?.*$/,
    ],
});
