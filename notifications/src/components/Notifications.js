/* eslint-disable react/prop-types */
import React, { useState, useEffect, useReducer } from "react";
import moment from "moment";
import {
  Container,
  Col,
  Button,
  Row,
  ButtonToolbar,
  ButtonGroup,
  Modal
} from "react-bootstrap";
import BackToTopButton from "./BackToTopButton";
import { Link } from "react-router-dom";
import styleGuide from "../styleGuide";
const { Icon } = styleGuide;
import {
  NotificationEventListener,
  MESSAGE_STATUS_READ,
  getMessages
} from "../notification-events";

const PAGE_SIZE = 100;

let lastScrollTop = 0;
let pageOffset = 0;

const sortColsMap = {
  module: {
    defaultDir: "asc"
  },
  created_at: {
    defaultDir: "desc"
  }
};

let scrollToBottomHandler = null;

const notificationsReducer = (notifications, event) => {
  switch (event.type) {
    case "set": {
      return [...event.notifications];
    }
    case "add": {
      const arr = [...notifications];
      event.notifications.forEach(notification => {
        if (!arr.find(n => notification.id === n.id)) {
          arr.push(notification);
        }
      });
      return arr;
    }
    case "update": {
      const index = notifications.findIndex(
        ({ id }) => id === event.notification.id
      );
      if (index !== -1) {
        return [
          ...notifications.slice(0, index),
          event.notification,
          ...notifications.slice(index + 1)
        ];
      }
      break;
    }
    case "delete": {
      return notifications.filter(notification => notification.id != event.id);
    }
    case "deleteAll": {
      return [];
    }
  }

  return notifications;
};

const mapNotification = ({
  id,
  status,
  message,
  module,
  created_at,
  link,
  icon
}) => {
  return {
    id,
    icon,
    text: message,
    module: module || "general",
    date: moment(created_at).fromNow(),
    status: status === MESSAGE_STATUS_READ ? "read" : "new",
    href: link || ""
  };
};

function formatMessage(message) {
  const statusProps = [
    {
      value: "reapplied",
      color: "#1F4A70" // navy blue
    },
    {
      value: "forwarded",
      color: "#1F4A70" // navy blue
    },
    {
      value: "approved",
      color: "#28A747" // green
    },
    {
      value: "reviewed",
      color: "#28A747" // green
    },
    {
      value: "rejected",
      color: "#D41B56" // red
    },
    {
      value: "pending",
      color: "#FFC107" // orange
    },
    // Data Import / item-master
    {
      value: "successfully",
      color: "#28A747" // green
    },
    {
      value: "Started",
      color: "#FFC107" // orange
    },
    {
      value: "Failed",
      color: "#D41B56" // red
    }
  ];
  const words = message.split(" ");
  return words.reduce((wordArr, word, index) => {
    const foundStatus = statusProps.find(
      statusProp => statusProp.value === word
    );
    return [
      ...wordArr,
      index === 0 ? null : " ",
      foundStatus ? (
        <span style={{ color: foundStatus.color, fontWeight: 600 }}>
          {foundStatus.value}
        </span>
      ) : (
        word
      )
    ];
  }, []);
}

const Notifications = ({ history, notificationEvents }) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, dispatchNotifications] = useReducer(
    notificationsReducer,
    []
  );
  const [order, setOrder] = useState({ col: "created_at", dir: "desc" });
  const [isLoading, setIsLoading] = useState(false);

  pageOffset = notifications.length;

  const loadMessages = (order => async () => {
    const { messages } = await getMessages({
      offset: pageOffset,
      limit: PAGE_SIZE,
      orderColumn: order.col,
      orderDirection: order.dir
    });
    return messages;
  })(order);

  const onScrolledToBottom = () => {
    if (scrollToBottomHandler) {
      clearTimeout(scrollToBottomHandler);
    }
    scrollToBottomHandler = setTimeout(() => {
      setIsLoading(true);
      loadMessages()
        .then(messages => {
          dispatchNotifications({
            type: "add",
            notifications: messages.map(mapNotification)
          });
          setIsLoading(false);
        })
        .catch(() => setIsLoading(false));
    }, 100);
  };

  const onScroll = () => {
    const scrollContainer =
      document.compatMode === "BackCompat"
        ? document.body
        : document.documentElement;
    const bottom =
      scrollContainer.scrollHeight - scrollContainer.scrollTop - 60 <=
      scrollContainer.clientHeight;
    if (
      !isLoading &&
      bottom &&
      !isLoading &&
      scrollContainer.scrollTop >= lastScrollTop &&
      pageOffset > 0
    ) {
      onScrolledToBottom();
    }
    lastScrollTop = scrollContainer.scrollTop;
  };

  const getSortIcon = (order => sortCol => {
    if (sortCol !== order.col) return "sort-off";
    if (order.dir === "desc") return "sort-descending";
    return "sort-ascending";
  })(order);

  useEffect(() => {
    window.addEventListener("scroll", onScroll, true);
    return () => {
      window.removeEventListener("scroll", onScroll);
      if (scrollToBottomHandler) {
        clearTimeout(scrollToBottomHandler);
      }
    };
  }, []);

  useEffect(() => {
    if (isLoading) return;
    setIsLoading(true);
    loadMessages()
      .then(messages => {
        dispatchNotifications({
          type: "set",
          notifications: messages.map(mapNotification)
        });
        setIsLoading(false);
      })
      .catch(() => setIsLoading(false));
  }, [order]);

  useEffect(() => {
    if (isLoading || pageOffset === 0) return;
    setIsLoading(true);
    loadMessages()
      .then(messages => {
        dispatchNotifications({
          type: "add",
          notifications: messages.map(mapNotification)
        });
        setIsLoading(false);
      })
      .catch(() => setIsLoading(false));
  }, [pageOffset]);

  useEffect(() => {
    const closeListener = notificationEvents.listen(
      new NotificationEventListener({
        onMessages: async () => {
          if (pageOffset > 0);
          dispatchNotifications({
            type: "deleteAll"
          });
          const messages = await loadMessages();
          dispatchNotifications({
            type: "set",
            notifications: messages.map(mapNotification)
          });
        },
        onUser: user => {
          setUnreadCount(user.totalUnreadMessageCount);
        },
        onMessageUpdate: message => {
          dispatchNotifications({
            type: "update",
            notification: mapNotification(message)
          });
        },
        onMessageRemove: messageId => {
          dispatchNotifications({
            type: "delete",
            id: messageId
          });
        },
        onAllMessageRemove: () => {
          dispatchNotifications({
            type: "deleteAll"
          });
        }
      })
    );
    return () => closeListener();
  }, []);

  const removeNotification = notification => {
    notificationEvents.removeMessage(notification.id);
  };

  const markRead = notification => {
    notificationEvents.markMessageRead(notification.id);
  };

  const markAllAsRead = () => {
    notificationEvents.markAllMessagesRead();
  };

  const clearNotifications = () => {
    notificationEvents.removeAllMessages();
  };
  const onBack = () => {
    history.goBack();
  };

  const onSort = (colName, order) => {
    return () => {
      let dir = sortColsMap[colName].defaultDir;
      if (colName === order.col) {
        dir = order.dir === "desc" ? "asc" : "desc";
      }
      dispatchNotifications({
        type: "deleteAll"
      });
      setOrder({
        col: colName,
        dir
      });
    };
  };

  return (
    <div className="notification_center">
      <Container>
        <ButtonToolbar className="toolbar-allignment no-print">
          <ButtonGroup className="mr-2">
            <Button
              variant="outline-primary"
              className="btn-sm"
              onClick={markAllAsRead}
            >
              Mark all as Read
            </Button>
          </ButtonGroup>
          <ClearAll clearDataHandler={clearNotifications} />
        </ButtonToolbar>
        <Row>
          <Col>
            <h3>
              <Icon
                style={{ cursor: "pointer" }}
                icon="arrow-left"
                size={20}
                onClick={onBack}
              />{" "}
              Notifications{unreadCount > 0 ? ` (${unreadCount})` : ""}
            </h3>
          </Col>
        </Row>
        <Row>
          <Col>
            {notifications ? (
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Notification</th>
                    <th
                      scope="col"
                      className="sort-col"
                      onClick={onSort("module", order)}
                    >
                      <Icon
                        icon={getSortIcon("module")}
                        size={20}
                        className="default"
                      />
                      Module
                    </th>
                    <th
                      scope="col"
                      className="sort-col"
                      onClick={onSort("created_at", order)}
                    >
                      <Icon
                        icon={getSortIcon("created_at")}
                        size={20}
                        className="default"
                      />
                      Date received
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody>
                  {notifications.map(notification => {
                    const isExternalLink = notification.href?.startsWith(
                      "http"
                    );
                    return (
                      <tr key={notification.id} className={notification.status}>
                        <td>
                          <Icon
                            icon={notification.icon}
                            size={30}
                            className="image-alignment text-primary"
                          />
                        </td>
                        <td>
                          {isExternalLink ? (
                            <a
                              href={notification.href}
                              target="_blank"
                              onClick={() => {
                                markRead(notification);
                              }}
                            >
                              {formatMessage(notification.text)}
                            </a>
                          ) : (
                            <Link
                              to={notification.href}
                              onClick={() => {
                                markRead(notification);
                              }}
                            >
                              {formatMessage(notification.text)}
                            </Link>
                          )}
                        </td>
                        <td>{notification.module}</td>
                        <td>{notification.date}</td>
                        <td>
                          <Button
                            variant="outline-primary"
                            className="btn-sm"
                            onClick={() => {
                              removeNotification(notification);
                            }}
                          >
                            X
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            ) : null}
          </Col>
        </Row>
        <Row className="notifications-loading">
          <Col
            className={`notifications-loading__${
              isLoading ? "visible" : "hidden"
            }`}
          >
            Loading...
          </Col>
        </Row>
      </Container>
      <BackToTopButton />
    </div>
  );
};

function ClearAll(props) {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  return (
    <>
      <Button onClick={handleShow} className="btn-sm" variant="outline-primary">
        Clear all
      </Button>
      <Modal show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title>Confirm clearing all notifications?</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to clear all notifications?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            variant="secondary"
            onClick={() => {
              handleClose();
              props.clearDataHandler();
            }}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default Notifications;
