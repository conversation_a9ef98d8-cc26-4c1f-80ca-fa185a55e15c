import React from "react";
import Notifications from "../components/Notifications";
import { render, screen } from "@testing-library/react";

jest.mock("../styleGuide");
jest.mock("../notification-events");

// #TODO needs to fix, seems this was not running in pipeline and became obsolete
let notifications;
beforeEach(() => {
  notifications = render(
    <Notifications
      notificationEvents={{
        listen: () => jest.fn()
      }}
    />
  );
});

const fakeData = [
  {
    id: "1",
    icon: "x",
    text:
      'The vessel "MOL Treasture" is created in draft. It is pending for your approval to takeover.',
    module: "Vessel Module",
    date: "20 Feb",
    status: "new",
    href: "/vessel/details/1/approval"
  },
  {
    id: "2",
    icon: "x",
    text: 'The vessel "Etiam Lacinia" is approved by Accounts.',
    module: "Vessel Module",
    date: "20 Feb",
    status: "new",
    href: "/vessel/details/2/approval"
  },
  {
    id: "3",
    icon: "x",
    text: 'The vessel "Lorem Ipsum" is rejected by Final Approver',
    module: "Vessel Module",
    date: "20 Feb",
    status: "read",
    href: "/vessel/details/3/approval"
  }
];

describe("<Notifications />", () => {
  xit("should have header with notifications count of 2 unread messages", () => {
    expect(screen.getByTestId("header-search-icon")).toBeInTheDocument();

    expect(notifications.find("h3").text()).toBe("Notifications (2)");
  });

  xit("should have header with notifications count of 1 unread messages, after I clicked remove message", () => {
    notifications
      .find("table button")
      .at(1)
      .simulate("click");
    expect(notifications.find("h3").text()).toBe("Notifications (1)");
  });

  xit("should have 2 rows of notification, after I remove one notification", () => {
    notifications
      .find("table button")
      .at(1)
      .simulate("click");
    expect(notifications.find("table tbody tr").length).toBe(2);
  });

  xit('should have 0 rows of notification, after I clicked "Clear all"', () => {
    notifications
      .find(".btn-outline-primary")
      .filterWhere(btn => btn.text() == "Clear all")
      .at(0)
      .simulate("click");
    notifications
      .find(".btn-secondary")
      .filterWhere(btn => btn.text() == "Confirm")
      .at(0)
      .simulate("click");
    expect(notifications.find("table tbody tr").length).toBe(0);
  });
});
