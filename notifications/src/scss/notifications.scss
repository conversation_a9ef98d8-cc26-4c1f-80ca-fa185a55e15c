.notification_center {

    tr {
        &.new {
            background-color:#F2F9FC!important;
        }
        a {
            text-decoration: none;
            color: inherit;
            display: contents;
        }
    }

    .container {
        margin-top: 100px;
    }

    .toolbar-allignment {
        float: right;
    }
}

.sort-col {
  cursor: pointer;
}

.notifications-loading {
  text-align: center;
  min-width: 50px;
  padding-bottom: 16px;

  &__visible {
    visibility: visible;
  }

  &__hidden {
    visibility: hidden;
  }
}

.back-to-top {
  .paris2-icon {
    position: fixed;
    bottom: 2rem;
    right: 5rem;
    color: #1e4a70;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  span {
    position: fixed;
    bottom: 0rem;
    right: 4rem;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;
  }
}
