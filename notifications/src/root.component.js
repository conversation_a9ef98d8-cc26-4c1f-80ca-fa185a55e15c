/* eslint-disable no-console */
import React from "react";
import Notifications from "./components/Notifications.js";
import * as localization from "@paris2/localization";
import "./scss/notifications.scss";
import { BrowserRouter } from "react-router-dom";

export default class Root extends React.Component {
  state = {
    hasError: false,
    i18n: null,
    languages: []
  };

  constructor(props) {
    super(props);
  }

  componentDidCatch(error, info) {
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, currentLanguage }) => {
    this.setState({ i18n, currentLanguage });
  };

  componentDidUnmount = async () => {
    localization.removeCallback(this.onLanguageChanged);
  };

  componentDidMount = async () => {
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
  };

  render() {
    const { i18n, currentLanguage } = this.state;
    const { notificationEvents } = this.props;
    if (!i18n || !currentLanguage) return null;
    return (
      <BrowserRouter>
        <Notifications
          history={this.props.history}
          notificationEvents={notificationEvents}
        />
      </BrowserRouter>
    );
  }
}
