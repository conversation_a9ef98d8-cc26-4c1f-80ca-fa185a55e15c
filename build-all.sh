#!/bin/bash

export ENV=${ENV:-dev2}
export BITBUCKET_USER_NAME=${BITBUCKET_USER_NAME}
export BITBUCKET_APP_PASSWORD=${BITBUCKET_APP_PASSWORD}
export CONFIG_BRANCH=${CONFIG_BRANCH}

./load-configuration.sh

source ./assets/build.sh

npm i &&

modules=(
  "navbar"
  "style-guide"
  "root-config"
  "localization"
  "notifications"
  "auth"
  "notification-events"
  "landing-page"
  "faq"
)

for module in "${modules[@]}"
do
  echo "building... ${module}"
  (cd $module && npm i && npm run build);
done
