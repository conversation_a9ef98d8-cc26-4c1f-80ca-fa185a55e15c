{"name": "paris2-web-base", "version": "1.0.0", "description": "", "scripts": {"build": "./build-all.sh", "deploy-dev": "./deploy-all.sh dev", "test": "./test-all.sh"}, "repository": {"type": "git", "url": "git+ssh://*****************/fleetshipteam/paris2-web-base.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/fleetshipteam/paris2-web-base#readme", "devDependencies": {"dotenv": "^8.2.0", "eslint-config-important-stuff": "^1.1.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-react": "^7.20.6", "prettier": "^1.19.1", "sonar-scanner": "^3.1.0"}}